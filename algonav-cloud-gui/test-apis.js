// Simple test script to verify API endpoints work
// This bypasses authentication for testing purposes

const { createClient } = require('@supabase/supabase-js');

// Mock Supabase client for testing
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'http://localhost:54321';
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'your-anon-key';

async function testTreeAPI() {
    console.log('\n=== Testing Tree API ===');
    
    try {
        // Test the RPC function directly
        const supabase = createClient(supabaseUrl, supabaseKey);
        
        const { data, error } = await supabase.rpc('get_tree_skeleton', { 
            with_counts: true 
        });
        
        if (error) {
            console.error('Tree API Error:', error);
            return;
        }
        
        console.log('Tree API Success:');
        console.log(JSON.stringify(data, null, 2));
        
    } catch (err) {
        console.error('Tree API Exception:', err.message);
    }
}

async function testVarNodesAPI() {
    console.log('\n=== Testing Var-Nodes API ===');
    
    try {
        const supabase = createClient(supabaseUrl, supabaseKey);
        
        // Test for ENVIRONMENT key
        const { data: categoryData, error: categoryError } = await supabase
            .from('categories')
            .select('id, path')
            .contains('variable_overrides', { 'ENVIRONMENT': null });
            
        const { data: datasetData, error: datasetError } = await supabase
            .from('datasets')
            .select('id, path')
            .contains('variable_overrides', { 'ENVIRONMENT': null });
        
        if (categoryError || datasetError) {
            console.error('Var-Nodes API Error:', categoryError || datasetError);
            return;
        }
        
        const results = [
            ...(categoryData || []).map(item => ({ ...item, kind: 'cat' })),
            ...(datasetData || []).map(item => ({ ...item, kind: 'ds' }))
        ];
        
        console.log('Var-Nodes API Success (ENVIRONMENT key):');
        console.log(JSON.stringify(results, null, 2));
        
    } catch (err) {
        console.error('Var-Nodes API Exception:', err.message);
    }
}

async function testJobEffectiveAPI() {
    console.log('\n=== Testing Job Effective API ===');
    
    try {
        const supabase = createClient(supabaseUrl, supabaseKey);
        
        const { data, error } = await supabase.rpc('merge_job_vars', {
            p_template_id: 1,
            p_dataset_ids: [15, 17]
        });
        
        if (error) {
            console.error('Job Effective API Error:', error);
            return;
        }
        
        console.log('Job Effective API Success:');
        console.log(JSON.stringify(data, null, 2));
        
    } catch (err) {
        console.error('Job Effective API Exception:', err.message);
    }
}

async function runTests() {
    console.log('Starting API Tests...');
    
    await testTreeAPI();
    await testVarNodesAPI();
    await testJobEffectiveAPI();
    
    console.log('\n=== Tests Complete ===');
}

// Run tests if this file is executed directly
if (require.main === module) {
    runTests().catch(console.error);
}

module.exports = { testTreeAPI, testVarNodesAPI, testJobEffectiveAPI };
