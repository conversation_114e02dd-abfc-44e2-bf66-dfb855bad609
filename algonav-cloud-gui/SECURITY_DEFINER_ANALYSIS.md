# Security Definer Funktionen - Sicherheitsanalyse

## Übersicht

Dieses Dokument listet alle PostgreSQL-Funktionen auf, die mit `SECURITY DEFINER` definiert sind, und bewertet potenzielle Sicherheitsrisiken.

## System-/Framework-Funktionen (Niedrige Priorität)

### GraphQL Schema (2 Funktionen)
- `graphql.get_schema_version` (Owner: supabase_admin)
- `graphql.increment_schema_version` (Owner: supabase_admin)

**Bewertung:** ✅ Sicher - Standard Supabase Framework-Funktionen

### HTTP/Network Funktionen (3 Funktionen)
- `net.http_get` (Owner: supabase_admin)
- `net.http_post` (Owner: supabase_admin)
- `supabase_functions.http_request` (Owner: supabase_functions_admin)

**Bewertung:** ✅ Sicher - Standard Supabase Network-Funktionen

### PgBouncer (1 Funktion)
- `pgbouncer.get_auth` (Owner: postgres)

**Bewertung:** ✅ Sicher - Standard Connection Pooling

### PgSodium Kryptografie (18 Funktionen)
- `pgsodium.create_key` (Owner: pgsodium_keymaker)
- `pgsodium.crypto_aead_det_decrypt` (Owner: pgsodium_keymaker)
- `pgsodium.crypto_aead_det_encrypt` (Owner: pgsodium_keymaker)
- `pgsodium.crypto_aead_ietf_decrypt` (Owner: pgsodium_keymaker)
- `pgsodium.crypto_aead_ietf_encrypt` (Owner: pgsodium_keymaker)
- `pgsodium.crypto_auth` (Owner: pgsodium_keymaker)
- `pgsodium.crypto_auth_hmacsha256` (Owner: pgsodium_keymaker)
- `pgsodium.crypto_auth_hmacsha256_verify` (Owner: pgsodium_keymaker)
- `pgsodium.crypto_auth_hmacsha512` (Owner: pgsodium_keymaker)
- `pgsodium.crypto_auth_hmacsha512_verify` (Owner: pgsodium_keymaker)
- `pgsodium.crypto_auth_verify` (Owner: pgsodium_keymaker)
- `pgsodium.crypto_generichash` (Owner: pgsodium_keymaker)
- `pgsodium.crypto_kdf_derive_from_key` (Owner: pgsodium_keymaker)
- `pgsodium.crypto_secretbox` (Owner: pgsodium_keymaker)
- `pgsodium.crypto_secretbox_open` (Owner: pgsodium_keymaker)
- `pgsodium.crypto_shorthash` (Owner: pgsodium_keymaker)
- `pgsodium.disable_security_label_trigger` (Owner: supabase_admin)
- `pgsodium.enable_security_label_trigger` (Owner: supabase_admin)
- `pgsodium.get_key_by_id` (Owner: supabase_admin)
- `pgsodium.get_key_by_name` (Owner: supabase_admin)
- `pgsodium.get_named_keys` (Owner: supabase_admin)
- `pgsodium.mask_role` (Owner: supabase_admin)
- `pgsodium.update_mask` (Owner: supabase_admin)

**Bewertung:** ✅ Sicher - Standard Kryptografie-Extension

## Anwendungsspezifische Funktionen (Hohe Priorität)

### Dataset Management (4 Funktionen)
- `public.create_dataset_with_files` (Owner: postgres) - **2 Überladungen**
- `public.update_dataset_with_files` (Owner: postgres) - **4 Überladungen**

**Bewertung:** ⚠️ **KRITISCH** - Benötigt Überprüfung
**Empfehlung:** 
- Input-Validierung überprüfen
- RLS-Policies validieren
- Benutzerberechtigungen explizit prüfen

### Variable/Template Management (6 Funktionen)
- `public.get_categories_with_key` (Owner: postgres)
- `public.get_datasets_with_key` (Owner: postgres)
- `public.get_templates_with_key` (Owner: postgres)
- `public.get_var_nodes_filtered` (Owner: postgres) - **NEU HINZUGEFÜGT**
- `public.get_var_nodes_with_active_status` (Owner: postgres)
- `public.get_variable_sources_by_dataset` (Owner: postgres)

**Bewertung:** ⚠️ **HOCH** - Benötigt Überprüfung
**Empfehlung:**
- Benutzer-ID Validierung in jeder Funktion
- RLS-Bypass vermeiden
- Alternative: SECURITY INVOKER mit expliziter Berechtigungsprüfung

### User/Role Management (3 Funktionen)
- `public.get_user_role` (Owner: postgres)
- `public.set_user_role` (Owner: postgres)
- `public.is_admin` (Owner: postgres)

**Bewertung:** 🔴 **SEHR KRITISCH** - Sofortige Überprüfung erforderlich
**Empfehlung:**
- Strenge Input-Validierung
- Audit-Logging implementieren
- Minimale Berechtigungen principle

### Utility Funktionen (4 Funktionen)
- `public.get_or_create_notification_state` (Owner: postgres)
- `public.get_tree_skeleton` (Owner: postgres)
- `public.handle_storage_change` (Owner: postgres)
- `public.merge_job_vars` (Owner: postgres)

**Bewertung:** ⚠️ **MITTEL** - Überprüfung empfohlen

## Empfohlene Maßnahmen

### Sofortige Maßnahmen (Kritisch)
1. **User/Role Management Funktionen überprüfen:**
   - `set_user_role`: Wer darf Rollen ändern?
   - `is_admin`: Wird korrekt validiert?
   - `get_user_role`: Kann missbraucht werden?

2. **Dataset Management Funktionen:**
   - Input-Sanitization überprüfen
   - File-Upload Validierung
   - Benutzerberechtigungen explizit prüfen

### Mittelfristige Maßnahmen
1. **Alternative Implementierung evaluieren:**
   ```sql
   -- Statt SECURITY DEFINER:
   CREATE FUNCTION get_var_nodes_filtered(...) 
   SECURITY INVOKER
   AS $$
   BEGIN
     -- Explizite Berechtigungsprüfung
     IF NOT EXISTS (SELECT 1 FROM datasets WHERE id = p_dataset_id AND user_id = auth.uid()) THEN
       RAISE EXCEPTION 'Access denied';
     END IF;
     -- Rest der Logik...
   END;
   $$;
   ```

2. **Audit-Logging implementieren:**
   - Alle SECURITY DEFINER Aufrufe loggen
   - Verdächtige Aktivitäten überwachen

3. **Principle of Least Privilege:**
   - Minimale notwendige Berechtigungen
   - Regelmäßige Berechtigungsreviews

### Langfristige Maßnahmen
1. **Code Review Process:**
   - Alle neuen SECURITY DEFINER Funktionen reviewen
   - Security Checklist erstellen

2. **Monitoring & Alerting:**
   - Ungewöhnliche Funktionsaufrufe überwachen
   - Automatische Alerts bei verdächtigen Aktivitäten

## Fazit

Von 47 SECURITY DEFINER Funktionen sind:
- **24** System/Framework-Funktionen (sicher)
- **23** Anwendungsspezifische Funktionen (benötigen Überprüfung)

**Priorität 1:** User/Role Management Funktionen (3)
**Priorität 2:** Dataset Management Funktionen (6)
**Priorität 3:** Variable/Template Management Funktionen (6)
**Priorität 4:** Utility Funktionen (4)

**Nächste Schritte:** Beginnen Sie mit der Überprüfung der Priorität 1 Funktionen und implementieren Sie explizite Berechtigungsprüfungen.