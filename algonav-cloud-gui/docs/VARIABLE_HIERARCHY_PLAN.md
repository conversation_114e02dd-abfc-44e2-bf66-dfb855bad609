# Variable Hierarchy: Overrides, Navigation, Constraints — Implementation Plan

Last updated: 2025‑08‑23

This plan operationalizes the meeting decisions for a hierarchical variable system with overrides, jump‑to level navigation, and level‑based constraints. It builds on the existing Variable Tree implementation in this repo.

## 0) TL;DR

- Add “Global” as explicit root node (level 0) and treat Dataset as level 999.
- Extend variable metadata with required + level constraints (minLevel/maxLevel).
- Implement Override/Specify and Go‑to‑defining‑level actions on badges and inputs.
- Enforce editability per level; show empty/disabled states accordingly.
- Provide counts and tooltips for overridden/inherited states; optionally add subtree counts.
- Unify file‑type variables with a dedicated input (`chooseFile`/`FilePicker`).

---

## 1) Current State (as of repo scan)

- UI
  - `app/variable-tree/page.tsx` renders the Variable Tree page with template selection.
  - `components/template/VariableTreeView.tsx` implements the 2‑column layout (left tree, right variables), status toggle, and save/reset controls via `useVariableTreeState`.
  - `components/template/VariableStatusBadge.tsx` shows colored badges with tooltips (active/overridden/defined‑higher/not‑set) and counts.
  - `components/template/VariableInputRenderer.tsx` bridges a variable entry to `InputRenderer` and shows a status badge and (focus) indicator.
  - `components/template/InputRenderer.tsx` renders concrete inputs from `gui.component_id`.
- Hooks & Utils
  - `lib/hooks/useVariableTree.ts` fetches `/api/variable-tree`, computes variable states globally and per context, exposes `focusedVariable`, and returns counts + active paths.
  - `lib/hooks/useVariableTreeState.ts` tracks local changes, groups by context (template/category/dataset), and persists via API.
  - `lib/utils/variableColors.ts` defines colors/labels for states; `lib/utils/variableState.ts` decides save context.
- API / Backend
  - GET `/api/variable-tree` calls RPC `get_variable_tree_with_context(templateId, userId)` (see `supabase/migrations/*get_variable_tree_with_context*.sql`) to return nested categories with dataset overrides and template vars.
  - PUT endpoints exist to update overrides:
    - `/api/variable-tree/template/[id]`
    - `/api/variable-tree/category/[id]`
    - `/api/variable-tree/dataset/[id]`
  - `services/dataProcessingService.ts` merges variables for jobs (template < dataset; category planned).

Gaps vs. meeting goals:
- No explicit “Global” node in the tree; template is separate in UI copy.
- No required/level‑constraints semantics on variables.
- No explicit “Override/Specify” and “Go to defining level” UI actions.
- Disabled/empty rendering per constraints not enforced.
- File variables not unified as standard component.
- Optional orange “overridden count” per subtree not available server‑side.

---

## 2) Data Model & Schema

Target metadata per variable (in Template JSON, source of truth):

```json
{
  "name": "disable_face",
  "title": "Disable Face",
  "type": "boolean",
  "data": null,
  "gui": {
    "component_id": "Checkbox",
    "label": "Disable Face",
    "required": false,
    "constraints": { "minLevel": 0, "maxLevel": 999 }
  }
}
```

- Add fields to template variable objects (stored in `global_job_templates.vars.vars[*]`):
  - `title?: string` and `type?: string` (for consistent labeling and input semantics)
  - `gui.required?: boolean` (default false; UI-level requirement)
  - `gui.constraints?: { minLevel?: number; maxLevel?: number }` (eq via minLevel == maxLevel)
- Convention constants:
  - Global = 0; Categories = 1..N (path‑depth based); Dataset = 999.
- Category/Dataset overrides keep value only; metadata inherited from template variable with same `name`.

DB migrations (Supabase):
- No table schema change required (JSONB). We add keys inside existing JSON.
- Create a data migration utility (idempotent) that adds missing `gui.required: false` and `gui.constraints` defaults into template JSONs; safe‑guard missing `title`.

---

## 3) Backend: RPC & Routes

RPC: `get_variable_tree_with_context`
- Extend output to propagate metadata for each variable instance:
  - `meta.required`, `meta.constraints` (copied from the variable's `gui.required` and `gui.constraints` on template level)
  - `level` on nodes; expose template level 0 and dataset pseudo‑level 999
  - Derived flags per context (optional server‑side to reduce client compute):
    - `editable_here: boolean` based on `constraints` and context level
    - `unset_required: boolean` if required and no effective value on path
    - `defining: { level: number, nodeId?: number, datasetId?: number, path: string }` for “go‑to”
    - `overridden_downstream_count` for category context (optional, see 7)

API Endpoints (augment existing):
- DELETE endpoints to remove an override (reset to inherited/global):
  - `DELETE /api/variable-tree/template/[id]?name=VAR`
  - `DELETE /api/variable-tree/category/[id]?name=VAR`
  - `DELETE /api/variable-tree/dataset/[id]?name=VAR`
  - Behavior: remove entry from `*.variable_overrides.vars` if present.
- GET `GET /api/variable-tree/effective?templateId=...&nodeId=...&datasetId=...`
  - Optional helper: returns effective value + source path for a variable or a batch of variables.

Security & Validation
- Keep `withAuth` wrapper. Validate ownership. Ensure inputs sanitized. Enforce name whitelist if needed.
- Server-side guard: Reject updates outside `minLevel`/`maxLevel` with 400 to prevent API bypass, even if constraints are primarily for GUI.

---

## 4) Frontend: UX, Actions, and Semantics

4.1 Global Root
- Update tree builder (`VariableTreeView.buildTreeFromVariableData`) to prepend a root item:
  - id: `global-root`, type: `global`, level: 0
  - label: `Global (TemplateName)`
  - variables: `data.template_variables`
  - children: existing category roots
- Ensure left pane defaults to Global expanded and selected.

4.2 Badges: Colors, Tooltips, Actions
- Keep colors: green=active, gray=defined‑higher, orange=overridden, red=not‑set (`lib/utils/variableColors.ts`).
- Extend `VariableStatusBadge` props to support action icons:
  - Override/Specify: activates variable at current level
  - Go to defining level (↗): focuses tree to the active source (use state from `useVariableTree`)
- Tooltip: include path string `Global → …` and state (“higher‑level defined”, “overridden down‑level”, counts).
- Optional: show `x/y overridden` when in orange, if counts available.

4.3 Inputs: Empty, Disabled, Override
- Extend `VariableInputRenderer` to accept `disabled` and `onOverride`:
  - If `data == null` and editable: render empty component (no default selection).
  - If not editable due to constraints: render disabled, hide Override.
  - If inherited (`defined-higher`) and editable: show “Override” button beside status to enable local value (pre‑fill from effective value or start empty; configurable default: pre‑fill).
- Add “Reset to inherited” for locally overridden vars (removes override via DELETE endpoint).
- Ensure all concrete inputs in `components/inputs/*` accept `disabled` and handle `null` gracefully.

4.4 Navigation: Go to defining level
- In `VariableTreeView`, implement `goTo(variableName)`:
  - Use `getVariableStateForContext` (or enriched RPC flags) to resolve `{nodeId,datasetId}` of the active instance.
  - Compute target tree id: `category-<id>` or `dataset-<id>` or `global-root` (template).
  - Expand ancestors, select target; scroll into view.
  - Expose handler to `VariableStatusBadge` and `VariableInputRenderer`.

4.5 Two‑Column Layout polish
- Keep left narrow tree; right wide “Settings” panel.
- Replace “Variables” caption with “Settings”. Show context chip (“Global”, “Category”, “Dataset”).

---

## 5) Level Constraints & Required

5.1 Level Computation
- Level 0 = Global
- Categories: derive from `node.level` (already present from path depth)
- Dataset = 999

5.2 Editability Rules
- Implement `isEditableAt(variableMeta, level): boolean`:
  - Respect `minLevel`/`maxLevel` from `variable.gui.constraints` if present (editable iff `minLevel <= level <= maxLevel`).
  - If no constraints → editable everywhere.
- Implement `isRequiredUnset(context)`: true if `required` and no effective value found along inheritance for the current subtree.

5.3 UI Behavior
- If not editable: disable input, hide override button.
- If required and unset: show red badge “Not set”; in Global view, also list required‑unset variables first.

---

## 6) Override Semantics

- Gray → Override → Green: creating a local override makes this level active; higher level remains but becomes “overridden”.
- Orange at current level: “somewhere deeper” different value exists. Optionally show `overridden_downstream_count`.
- Global not set, deeper set: Global badge gray (optionally lighter tone to differentiate “unset upstream” vs “inherited”).
- Reset: removing an override reveals the next effective value in the chain.

---

## 7) Counts and Performance

Optional but desirable: server‑side counts to avoid heavy client traversal.
- Per (variable, category‑node): `overridden_downstream_count`, `instances_in_subtree`.
- Compute in SQL with CTEs leveraging `categories.path` to filter subtree; count differing values by comparing to effective at the current node.
- Wire through RPC so badges can render `x/y` without client recursion.



## 9) Edge Cases

- Boolean variables (e.g., Disable Face): allow override at dataset when Global is set; respect constraints.
- Required without default: render empty but focusable input (no crashes); red badge until set somewhere allowed.
- Variables set across many datasets: show orange markers and optional jump list; jump chooses first/nearest by default.

---

## 10) Deliverables by Area

Backend (Supabase + API)
- [ ] Extend RPC `get_variable_tree_with_context` to include variable `meta` and optional computed flags.
- [ ] Add DELETE endpoints to remove overrides at each level.
- [ ] Optional: add effective‑value helper endpoint.
- [ ] Data migration script to backfill `gui.required: false` and `gui.constraints` defaults in template JSON.

Frontend
- [ ] Add Global root in `VariableTreeView`.
- [ ] Actions on `VariableStatusBadge`: Override/Specify and Go‑to (↗).
- [ ] Editability enforcement and empty/disabled states in `VariableInputRenderer` and inputs.
- [ ] “Reset to inherited” control per variable.
- [ ] Go‑to logic (expand/select tree); focus indicator remains as is.
- [ ] Optional: show `x/y overridden` when counts are available.
- [ ] New `FilePicker` input; integrate with dataset context.

UX/Copy
- [ ] Rename right panel to “Settings”; add context chip (“Global”, “Kampagne/Category”, “Dataset”).
- [ ] Tooltip strings per meeting language; path renders with `→` separators.
- [ ] Color palette double‑check for contrast (WCAG AA).

Testing
- [ ] Unit: `isEditableAt`, required/unset resolver, go‑to target calculation.
- [ ] Component: `VariableStatusBadge` actions, disabled/empty inputs, reset/override flows.
- [ ] API: DELETE override routes; RPC includes meta and flags.
- [ ] Integration: end‑to‑end edit/override/save/jump.

---

## 11) Implementation Notes (File Map)

- UI
  - `components/template/VariableTreeView.tsx`: add Global root; go‑to logic; integrate counts; pass handlers.
  - `components/template/VariableStatusBadge.tsx`: add action icons; hook up tooltips.
  - `components/template/VariableInputRenderer.tsx`: enforce disabled/empty; add Override + Reset; surface actions.
  - `components/template/InputRenderer.tsx` + `components/inputs/*`: accept `disabled`; handle `null` values.
  - `components/template/CustomTreeItem.tsx`: no change unless indicating per‑node markers.
- Hooks
  - `lib/hooks/useVariableTree.ts`: if RPC enriched, consume `editable_here`, `unset_required`, counts, and `defining` directly. Otherwise, add small helpers to compute editability from constraints.
  - `lib/hooks/useVariableTreeState.ts`: add DELETE flows for reset; keep grouping by context.
- API
  - `app/api/variable-tree/*`: add DELETE handlers; extend PUT validation; keep `withAuth`.
- Supabase
  - Migrations adjusting RPC to include metadata and counts; add comments and grants.

---

## 12) Open Decisions (from meeting)

1) Gray nuances: implement two grays or a tooltip qualifier only → propose: keep single gray for MVP, add tooltip text to distinguish.
2) Orange counters: implement server‑side for accuracy/perf. If deferred, hide counter and keep tooltip list.
3) Files in tree: show badges in right panel only; optional linking in tree later.
4) Constraints: standardize on `minLevel`/`maxLevel` only; revisit `allowedLevels` later if non-contiguous needs arise.

---

## 13) Mini Examples

Dataset‑only, required:
```json
{
  "name": "start_time",
  "title": "Startzeit",
  "type": "datetime",
  "data": null,
  "gui": {
    "component_id": "DatetimePicker",
    "label": "Startzeit",
    "required": true,
    "constraints": { "minLevel": 999, "maxLevel": 999 }
  }
}
```

Category‑only:
```json
{
  "name": "campaign_mode",
  "title": "Kampagnenmodus",
  "type": "enum",
  "gui": { 
    "component_id": "RadioButtons",
    "constraints": { "minLevel": 1, "maxLevel": 1 },
    "items": [
    {"label":"A","value":"A"}, {"label":"B","value":"B"}
    ]
  }
}
```

---

## 14) Rollout & Backwards Compatibility

- Existing templates remain valid; defaults applied (`gui.required: false`, constraints omitted → editable everywhere).
- UI features degrade gracefully if RPC lacks enriched flags (client computes basic state from current data).
- Endpoints are additive; DELETE is new and safe to add.

---

## 15) Estimates (MVP)

- Backend (RPC + DELETE routes): 1.5–2.5 days
- Frontend (Global root, actions, editability, reset, go‑to): 2–3 days
- FilePicker input: 0.5–1 day
- Tests & polish: 1–1.5 days

Total MVP: ~5–8 days depending on counters scope and RPC enrichment timing.
