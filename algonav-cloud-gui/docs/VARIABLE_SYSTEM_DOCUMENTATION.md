# Variable System Documentation

## Übersicht

Das Variable System in AlgoNav Cloud GUI ermöglicht die hierarchische Verwaltung von Konfigurationsvariablen mit Override-Mechanismen. Variablen können auf verschiedenen Ebenen definiert werden und folgen einer klaren Vererbungshierarchie.

## Architektur und Dateistruktur

Das Variable System wurde reorganisiert für bessere Wartbarkeit und klare Trennung der Verantwortlichkeiten:

### Neue Dateistruktur

```
algonav-cloud-gui/
├── types/
│   └── variable.ts                    # Zentrale Type-Definitionen
├── lib/
│   └── utils/
│       ├── variableState.ts          # Variable State Management
│       ├── changeTracking.ts         # Change Tracking Utilities
│       └── variableMapping.ts        # Mapping zwischen Formaten
├── lib/hooks/
│   ├── useVariableTree.ts            # Data Fetching Hook
│   └── useVariableTreeState.ts       # State Management Hook
├── components/template/
│   ├── VariableTreeView.tsx          # Haupt-UI-Komponente
│   ├── VariableInputRenderer.tsx     # Variable Input Rendering
│   └── InputRenderer.tsx             # Spezifische Input-Komponenten
└── app/api/variable-tree/
    ├── route.ts                      # Haupt-API-Endpunkt
    ├── template/[id]/route.ts        # Template Variable Updates
    ├── category/[id]/route.ts        # Category Variable Updates
    └── dataset/[id]/route.ts         # Dataset Variable Updates
```

### Trennung der Verantwortlichkeiten

1. **Types** (`types/variable.ts`): Alle variable-bezogenen TypeScript-Interfaces
2. **State Management** (`lib/utils/variableState.ts`): Funktionen zur Bestimmung des Variable-Status
3. **Change Tracking** (`lib/utils/changeTracking.ts`): Klassen und Funktionen für Änderungsverfolgung
4. **Mapping** (`lib/utils/variableMapping.ts`): Konvertierung zwischen verschiedenen Variable-Formaten

## Datenbank-Schema

### Tabellen-Struktur
Template variables: In global_job_templates.vars (JSONB)
Category variables: In categories.variable_overrides (JSONB)
Dataset variables: In datasets.variable_overrides (JSONB)

### Variable-Objekt-Struktur

**Korrekte Struktur für alle Ebenen:**
```json
{
  "vars": [
    {
      "name": "VARIABLE_NAME",
      "data": "variable_value",
      "links": [],
      "gui": {
        "component_id": "TextInput",
        "label": "Variable Label",
        "group": "Section Name",
        "order": 100,
        "required": false,
        "constraints": { "minLevel": 0, "maxLevel": 999 }
      }
    }
  ]
}
```

**❌ Falsche Struktur (Bug-anfällig):**
```json
{
  "vars": [...],
  "VARIABLE_NAME": "variable_value"  // Separate Root-Property - NICHT verwenden!
}
```

**Wichtige Regeln:**
- Variablen müssen IMMER als Objekte im `vars` Array gespeichert werden
- Der Wert gehört in das `data` Feld des Variable-Objekts
- Niemals separate Root-Level-Properties für Variable-Werte erstellen
- Diese Struktur gilt für Template-, Category- und Dataset-Variablen

### GUI-Konfiguration Format

```json
{
  "component_id": "Checkbox|CheckboxGroup|TextInput|NumberInput|...",
  "label": "Human-readable label",
  "tooltip": "Help text",
  "group": "Section grouping",
  "order": 100,
  "required": false,
  "constraints": { "minLevel": 0, "maxLevel": 999 },
  "items": [  // Für CheckboxGroup
    {"label": "GPS", "value": "G"},
    {"label": "Galileo", "value": "E"}
  ],
  "min_checked": 1,  // Validierung
  "max_checked": 999,
  "msg_below_min": "Error message"
}
```

### Constraints und Required

- Ebenen-Mapping: `Global = 0`, `Kategorien = 1..N` (Tiefe), `Dataset = 999`.
- `gui.constraints.minLevel`/`maxLevel`: Steuern, auf welchen Ebenen die Variable bearbeitbar ist.
  - Editierbar, wenn `minLevel <= level <= maxLevel`.
  - `minLevel == maxLevel` entspricht „nur diese Ebene“.
- `gui.required`: Markiert die Variable als erforderlich. UI zeigt roten Zustand, wenn entlang der Vererbung kein effektiver Wert vorhanden ist.
- Serverseitiger Guard empfohlen: API-Updates außerhalb der erlaubten Ebenen mit 400 ablehnen.

## Hierarchie und Override-System

### Vererbungshierarchie (niedrigste zu höchste Priorität)

1. **Template-Ebene** (Basis)
   - Definiert Standard-Werte für alle Jobs mit diesem Template
   - Niedrigste Priorität

2. **Kategorie-Ebene** (Override 1)
   - Überschreibt Template-Werte für spezifische Kategorien
   - Mittlere Priorität
   - Vererbung: Unterkategorien erben von Elternkategorien

3. **Dataset-Ebene** (Override 2)
   - Überschreibt alle höheren Ebenen für spezifische Datasets
   - Höchste Priorität

### Override-Regeln

- **Aktive Variable**: Die Variable mit der höchsten Priorität in der Hierarchie
- **Überschriebene Variable**: Variable existiert, wird aber von höherer Ebene überschrieben
- **Vererbung**: Unterkategorien erben Variablen von Elternkategorien
- **Vollständige Überschreibung**: Höhere Ebene überschreibt komplett (nicht nur einzelne Eigenschaften)
- **Required/Constraints in der UI**: Inputs sind auf verbotenen Ebenen deaktiviert; „Override“ ist nur möglich, wenn die aktuelle Ebene innerhalb `gui.constraints` liegt. Fehlende erforderliche Werte werden (insb. auf Global) hervorgehoben.

## Datenbank-Funktionen

### Haupt-RPC-Funktion

```sql
get_variable_tree_with_context(p_template_id INTEGER, p_user_id UUID)
```

**Rückgabe-Struktur:**
```json
{
  "template_id": 8,
  "template_variables": [
    {
      "name": "DISABLE_PHASE",
      "data": false,
      "gui": {...},
      "source_level": "Template",
      "is_active": true,
      "is_overridden": false
    }
  ],
  "tree": [
    {
      "id": 19,
      "name": "Root-Kampagne",
      "type": "category",
      "level": 0,
      "variables": [...],
      "children": [
        {
          "id": 21,
          "name": "Sub-Kampagne1",
          "type": "category",
          "level": 1,
          "datasets": [
            {
              "id": 41,
              "name": "mergetest",
              "type": "dataset",
              "variables": [
                {
                  "name": "GNSSES",
                  "data": ["G", "E", "R"],
                  "gui": {...}
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}
```

## API-Endpunkte

### Haupt-Route: `/api/variable-tree`

**Datei:** `app/api/variable-tree/route.ts`

**Import:**
```typescript
import { VariableWithContext, VariableTreeNode, VariableTreeResponse } from '@/types/variable';
```

**Parameter:**
- `templateId`: Template ID (required)

**Authentifizierung:** `withAuth` Middleware

**Rückgabe:** `VariableTreeResponse`

### Variable Update Endpunkte

#### Template Variables: `PUT /api/variable-tree/template/[id]`

**Datei:** `app/api/variable-tree/template/[id]/route.ts`

**Funktionalität:**
- Aktualisiert Template-Variablen in `global_job_templates.vars`
- Erhält `variableOverrides: { [variableName]: newValue }`
- Aktualisiert `data` Feld in bestehenden Variable-Objekten
- Erstellt neue Variable-Objekte für nicht-existierende Variablen

#### Category Variables: `PUT /api/variable-tree/category/[id]`

**Datei:** `app/api/variable-tree/category/[id]/route.ts`

**Funktionalität:**
- Aktualisiert Kategorie-Variable-Overrides in `categories.variable_overrides`
- Erhält `variableOverrides: { [variableName]: newValue }`
- Aktualisiert `data` Feld in bestehenden Variable-Objekten
- Erstellt neue Variable-Objekte für nicht-existierende Variablen

#### Dataset Variables: `PUT /api/variable-tree/dataset/[id]`

**Datei:** `app/api/variable-tree/dataset/[id]/route.ts`

**Funktionalität:**
- Aktualisiert Dataset-Variable-Overrides in `datasets.variable_overrides`
- Erhält `variableOverrides: { [variableName]: newValue }`
- Aktualisiert `data` Feld in bestehenden Variable-Objekten
- Erstellt neue Variable-Objekte für nicht-existierende Variablen

### TypeScript Interfaces

**Zentrale Type-Definitionen:** `types/variable.ts`

```typescript
// Haupt-Variable-Interface mit Kontext-Informationen
export interface VariableWithContext {
  name: string;
  source_level: 'Template' | 'Category' | 'Dataset';
  is_active: boolean;
  is_overridden: boolean;
  value: any;
  data?: any; // Alias für value
  gui_config?: any; // GUI-Konfiguration aus Datenbank
  gui?: any; // GUI-Konfiguration für InputRenderer
  links?: any[];
}

// Tree-Node für hierarchische Darstellung
export interface VariableTreeNode {
  id: number;
  name: string;
  description?: string;
  type: 'category' | 'dataset';
  level?: number;
  variables: VariableWithContext[];
  datasets?: VariableTreeNode[];
  children?: VariableTreeNode[];
}

// API-Response-Format
export interface VariableTreeResponse {
  template_id: number;
  template_variables: VariableWithContext[];
  tree: VariableTreeNode[];
}

// Change-Tracking für Batch-Updates
export interface VariableChange {
  variableName: string;
  oldValue: any;
  newValue: any;
  saveContext: VariableSaveContext;
  originalVariable: VariableWithContext;
}

// Save-Context für Variable-Updates
export interface VariableSaveContext {
  saveLevel: 'template' | 'category' | 'dataset';
  targetId?: number;
  targetType?: 'category' | 'dataset';
}
```

### Import-Patterns

**Neue empfohlene Import-Pfade:**

```typescript
// Type-Definitionen
import {
  VariableWithContext,
  VariableTreeNode,
  VariableTreeResponse,
  VariableChange,
  VariableSaveContext
} from '@/types/variable';

// State Management
import { determineVariableSaveContext } from '@/lib/utils/variableState';

// Change Tracking
import {
  VariableChangeTracker,
  groupChangesByContext
} from '@/lib/utils/changeTracking';

// Format-Mapping
import {
  variableWithContextToTemplateVariable,
  templateVariableToVariableWithContext
} from '@/lib/utils/variableMapping';
```

## React Hooks

### useVariableTree

**Datei:** `lib/hooks/useVariableTree.ts`

**Import:**
```typescript
import { useVariableTree } from '@/lib/hooks/useVariableTree';
import { VariableTreeResponse, VariableWithContext } from '@/types/variable';
```

**Funktionalität:**
```typescript
const {
  data,                    // VariableTreeResponse
  loading,                 // boolean
  error,                   // string | null
  refetch,                 // () => Promise<void>
  getVariablesByName,      // (name: string) => VariableWithContext[]
  getVariableState,        // (name: string) => VariableState
  getVariableStateForContext // (name: string, nodeId?, datasetId?) => VariableState
} = useVariableTree({ templateId, enabled: true });
```

### useVariableTreeState

**Datei:** `lib/hooks/useVariableTreeState.ts`

**Import:**
```typescript
import { useVariableTreeState } from '@/lib/hooks/useVariableTreeState';
import { VariableWithContext, VariableChange } from '@/types/variable';
import { VariableChangeTracker } from '@/lib/utils/changeTracking';
```

**Funktionalität:**
```typescript
const {
  variableChanges,         // Map<string, any>
  hasChanges,              // boolean
  updateVariable,          // (name, value, original, nodeId?, nodeType?) => void
  resetVariable,           // (name: string) => void
  resetAllChanges,         // () => void
  saveChanges,             // () => Promise<void>
  isSaving,                // boolean
  saveError,               // string | null
  getVariableValue,        // (name: string, originalValue: any) => any
  isVariableChanged        // (name: string) => boolean
} = useVariableTreeState({ templateId, onSaveSuccess?, onSaveError? });
```

**React Query Integration:**
- Verwendet `useMutation` für Template-, Kategorie- und Dataset-Updates
- Automatische Cache-Invalidierung nach erfolgreichen Änderungen
- Optimistische Updates und Error-Handling
- Folgt dem etablierten API-Pattern der Codebase

**API Service Integration:**
```typescript
// API Service Funktionen (lib/services/api.ts)
api.updateTemplateVariables(templateId, variableOverrides)
api.updateCategoryVariables(categoryId, variableOverrides)
api.updateDatasetVariables(datasetId, variableOverrides)

// Verwendung in useVariableTreeState:
updateTemplateMutation.mutateAsync({ templateId, variableOverrides })
updateCategoryMutation.mutateAsync({ categoryId, variableOverrides })
updateDatasetMutation.mutateAsync({ datasetId, variableOverrides })
```

### Wichtige Funktionen

1. **getVariablesByName(variableName)**
   - Findet alle Instanzen einer Variable in der gesamten Hierarchie
   - Sucht in: Template-Variablen, Kategorie-Variablen, Dataset-Variablen
   - Normalisiert Dataset/Kategorie-Variablen zu VariableWithContext Format

2. **getVariableState(variableName)**
   - Berechnet den Status einer Variable basierend auf Override-Regeln
   - Rückgabe: `{ primaryState, secondaryState, counts, activeVariable, overriddenBy }`

3. **getVariableStateForContext(variableName, nodeId?, datasetId?)**
   - Wie getVariableState, aber für spezifischen Kontext (Kategorie/Dataset)

## Utility-Funktionen

### Variable State Management

**Datei:** `lib/utils/variableState.ts`

```typescript
import { determineVariableSaveContext } from '@/lib/utils/variableState';
import { VariableWithContext, VariableSaveContext } from '@/types/variable';

// Bestimmt wo eine Variable gespeichert werden soll
const saveContext: VariableSaveContext = determineVariableSaveContext(
  variableWithContext,
  nodeId?,
  nodeType?
);
```

### Change Tracking

**Datei:** `lib/utils/changeTracking.ts`

```typescript
import { VariableChangeTracker, groupChangesByContext } from '@/lib/utils/changeTracking';
import { VariableChange } from '@/types/variable';

// Change Tracker für Batch-Updates
const tracker = new VariableChangeTracker();
tracker.addChange(variableName, oldValue, newValue, originalVariable, nodeId, nodeType);

// Gruppierung von Änderungen nach Kontext
const groupedChanges = groupChangesByContext(tracker.getChanges());
```

### Variable Mapping

**Datei:** `lib/utils/variableMapping.ts`

```typescript
import {
  variableWithContextToTemplateVariable,
  templateVariableToVariableWithContext
} from '@/lib/utils/variableMapping';
import { VariableWithContext } from '@/types/variable';
import { TemplateVariable } from '@/components/template/types/template';

// Konvertierung für InputRenderer
const templateVar = variableWithContextToTemplateVariable(variableWithContext, contextInfo);

// Rück-Konvertierung für API-Updates
const variableWithContext = templateVariableToVariableWithContext(templateVar, originalContext);
```

## UI-Komponenten

### VariableTreeView

**Datei:** `components/template/VariableTreeView.tsx`

**Import:**
```typescript
import { VariableTreeView } from '@/components/template/VariableTreeView';
import { VariableWithContext } from '@/types/variable';
```

**Funktionalität:**
- Zeigt hierarchischen Baum von Kategorien und Datasets
- Toggle zwischen Status-Badges und Input-Komponenten
- Zwei-Spalten-Layout: Hierarchie links, Variablen rechts

### VariableInputRenderer

**Datei:** `components/template/VariableInputRenderer.tsx`

**Import:**
```typescript
import { VariableInputRenderer } from '@/components/template/VariableInputRenderer';
import { VariableWithContext } from '@/types/variable';
```

**Funktionalität:**
- Rendert Variable als Input-Komponente basierend auf GUI-Konfiguration
- Konvertiert VariableWithContext zu TemplateVariable Format
- Zeigt Status-Indikatoren und Override-Markierungen

### InputRenderer

**Datei:** `components/template/InputRenderer.tsx`

**Funktionalität:**
- Rendert spezifische Input-Komponenten basierend auf `component_id`
- Unterstützt: Checkbox, CheckboxGroup, TextInput, NumberInput, etc.
- Verwendet Material-UI Komponenten

## Datenfluss

### 1. Datenbank → API

```
Datenbank (RPC) → get_variable_tree_with_context()
                ↓
API Route (/api/variable-tree) → VariableTreeResponse
```

### 2. API → React Hooks

```
API Response → useVariableTree Hook (Data Fetching)
             ↓
Normalisierte Daten + Helper-Funktionen

API Functions → useVariableTreeState Hook (State Management)
              ↓
React Query Mutations + Change Tracking
```

### 3. Hooks → UI Komponenten

```
useVariableTree → VariableTreeView (Display)
                ↓
getVariablesByName() → VariableInputRenderer
                     ↓
GUI Config → InputRenderer → Material-UI Komponente

useVariableTreeState → VariableInputRenderer (State Management)
                     ↓
updateVariable() → React Query Mutations → API Functions
```

### 4. Variable Lookup Prozess

```
1. getAllVariablesForContext(nodeId, datasetId)
   ↓
2. getVariablesByName(variableName) für jede Variable
   ↓
3. Findet alle Instanzen in Hierarchie
   ↓
4. getVariableStateForContext() berechnet aktive Variable
   ↓
5. VariableInputRenderer rendert Input-Komponente
```

## Debugging

### Console Logs

- `useVariableTree`: API-Aufrufe und Daten-Normalisierung
- `VariableTreeView`: Tree-Building und Variable-Lookup
- `VariableInputRenderer`: Variable-Rendering und GUI-Konfiguration

### Wichtige Debug-Punkte

1. **Variable nicht gefunden**: Prüfe `getVariablesByName()` Rückgabe
2. **Falsche GUI-Komponente**: Prüfe `variable.gui.component_id`
3. **Override-Probleme**: Prüfe `getVariableState()` Ergebnis
4. **Hierarchie-Probleme**: Prüfe Tree-Struktur in `data.tree`
5. **Input nicht responsiv**: Prüfe ob `currentValue` korrekt an `VariableInputRenderer` übergeben wird
6. **Falsche API-Endpunkt-Aufrufe**: Prüfe Context-ID-Bestimmung in `VariableTreeView`
7. **Datenstruktur-Korruption**: Prüfe ob Variable-Updates `data` Feld aktualisieren statt Root-Properties zu erstellen

## Erweiterungen

### Neue Input-Komponente hinzufügen

1. Komponente in `InputRenderer.tsx` registrieren
2. GUI-Konfiguration in Datenbank definieren
3. TypeScript-Typen erweitern falls nötig

### Neue Override-Ebene hinzufügen

1. Neue Tabelle in Datenbank erstellen
2. RPC-Funktion `get_variable_tree_with_context` erweitern
3. `useVariableTree` Hook anpassen
4. UI-Komponenten erweitern

## Beispiel-Szenarien

### Szenario 1: Template-Variable mit Dataset-Override

**Template-Ebene:**
```json
{
  "name": "DISABLE_PHASE",
  "data": false,
  "gui": {"component_id": "Checkbox", "label": "Disable Phase"},
  "source_level": "Template"
}
```

**Dataset-Ebene (Override):**
```json
{
  "name": "DISABLE_PHASE",
  "data": true,
  "source_level": "Dataset"
}
```

**Ergebnis:** Dataset-Wert (true) wird verwendet, GUI-Config vom Template übernommen.

### Szenario 2: Dataset-spezifische Variable

**Nur Dataset-Ebene:**
```json
{
  "name": "GNSSES",
  "data": ["G", "E", "R"],
  "gui": {
    "component_id": "CheckboxGroup",
    "items": [
      {"label": "GPS", "value": "G"},
      {"label": "Galileo", "value": "E"}
    ]
  },
  "source_level": "Dataset"
}
```

**Ergebnis:** Variable nur für dieses Dataset verfügbar.

### Szenario 3: Kategorie-Vererbung

**Root-Kategorie:**
```json
{"name": "ENVIRONMENT", "data": "outdoor"}
```

**Sub-Kategorie:** (erbt automatisch)
- Alle Datasets in Sub-Kategorie haben `ENVIRONMENT = "outdoor"`
- Kann durch Dataset-Variable überschrieben werden

## Performance-Überlegungen

### Caching
- `useVariableTree` Hook cached API-Responses
- Automatisches Refetch alle 30 Sekunden (konfigurierbar)
- Manuelle Refetch-Funktion verfügbar

### Optimierungen
- RPC-Funktion berechnet Override-Status in Datenbank
- Frontend normalisiert nur Datenformat
- Tree-Building erfolgt einmalig beim Laden

## Fehlerbehandlung

### Häufige Probleme

1. **Variable ohne GUI-Config wird nicht als Input angezeigt**
   - Lösung: `gui.component_id` in Datenbank setzen

2. **Dataset-Variable wird nicht gefunden**
   - Lösung: Prüfe `getVariablesByName()` Normalisierung

3. **Override funktioniert nicht**
   - Lösung: Prüfe Hierarchie-Pfad in RPC-Funktion

4. **Input-Komponente rendert nicht**
   - Lösung: Prüfe `InputRenderer` Komponenten-Mapping

5. **Variable-Inputs reagieren nicht auf Klicks**
   - Symptom: Checkboxes/Inputs zeigen keine visuellen Updates
   - Lösung: Prüfe ob `currentValue` korrekt von State-Management übergeben wird
   - Debug: Console-Logs in `InputRenderer.handleChange()` und `updateVariable()`

6. **Falsche Dataset/Category ID in API-Aufrufen**
   - Symptom: API-Aufrufe verwenden Category ID statt Dataset ID
   - Lösung: Prüfe Context-ID-Bestimmung in `VariableTreeView.onChange`
   - Debug: Console-Log `contextId` vor `updateVariable()` Aufruf

7. **Variable-Datenstruktur korrupt nach Updates**
   - Symptom: Separate Root-Properties statt `data` Feld-Updates
   - Lösung: Prüfe API-Endpunkt-Implementierung (Template/Category/Dataset)
   - Debug: Datenbank-Query nach Variable-Update prüfen

8. **Doppelte Save/Reset-Buttons**
   - Symptom: UI zeigt mehrere Save-Buttons
   - Lösung: Prüfe auf duplizierte `{hasChanges && (...)}` Blöcke

9. **URL-Parameter werden ignoriert**
   - Symptom: `?templateId=X` in URL hat keine Wirkung
   - Lösung: Prüfe `useSearchParams` Implementation in Page-Komponente

## Migration und Updates

### Datenbank-Änderungen
- Neue GUI-Komponenten: Erweitere `gui` JSONB-Feld
- Neue Override-Ebenen: Neue Tabelle + RPC-Update
- Schema-Änderungen: Migration-Scripts verwenden

### Code-Änderungen
- TypeScript-Interfaces in `types/variable.ts` aktualisieren
- Utility-Funktionen in `lib/utils/` erweitern
- Hook-Funktionen erweitern
- UI-Komponenten anpassen

## Reorganisation und Migration

### Neue Architektur-Vorteile

1. **Klare Trennung der Verantwortlichkeiten**: Types, State Management und Change Tracking sind getrennt
2. **Bessere Auffindbarkeit**: Types sind in einem dedizierten `types/` Verzeichnis
3. **Konsistent mit bestehenden Patterns**: Folgt dem etablierten Pattern eines `types/` Verzeichnisses
4. **Verbesserte Wartbarkeit**: Verwandte Funktionalität ist gruppiert
5. **Saubere Imports**: Keine Imports von Types aus API-Route-Dateien mehr
6. **Zukunftssicher**: Einfach erweiterbar mit zusätzlichen variable-bezogenen Utilities

### Migration von alten Import-Pfaden

**Alt (deprecated):**
```typescript
// Nicht mehr empfohlen
import { VariableWithContext } from '@/app/api/variable-tree/route';
import { VariableChangeTracker } from '@/components/template/utils/variableMapping';
```

**Neu (empfohlen):**
```typescript
// Neue empfohlene Pfade
import { VariableWithContext } from '@/types/variable';
import { VariableChangeTracker } from '@/lib/utils/changeTracking';
import { determineVariableSaveContext } from '@/lib/utils/variableState';
import { variableWithContextToTemplateVariable } from '@/lib/utils/variableMapping';
```

### Vollständiges Verwendungsbeispiel

```typescript
import React from 'react';
import {
  VariableWithContext,
  VariableChange,
  VariableSaveContext
} from '@/types/variable';
import { useVariableTree } from '@/lib/hooks/useVariableTree';
import { useVariableTreeState } from '@/lib/hooks/useVariableTreeState';
import { VariableChangeTracker } from '@/lib/utils/changeTracking';
import { determineVariableSaveContext } from '@/lib/utils/variableState';
import { variableWithContextToTemplateVariable } from '@/lib/utils/variableMapping';
import { VariableTreeView } from '@/components/template/VariableTreeView';

const MyVariableComponent: React.FC<{ templateId: number }> = ({ templateId }) => {
  // Data Fetching
  const {
    data,
    loading,
    error,
    getVariablesByName,
    getVariableState
  } = useVariableTree({ templateId, enabled: true });

  // State Management
  const {
    hasChanges,
    updateVariable,
    saveChanges,
    isSaving
  } = useVariableTreeState({
    templateId,
    onSaveSuccess: () => console.log('Variables saved successfully'),
    onSaveError: (error) => console.error('Save failed:', error)
  });

  // Variable Processing
  const processVariable = (variable: VariableWithContext, nodeId?: number) => {
    // Determine save context
    const saveContext = determineVariableSaveContext(variable, nodeId, 'category');

    // Convert for input rendering
    const templateVariable = variableWithContextToTemplateVariable(variable, {
      nodeId,
      nodeType: 'category'
    });

    return { saveContext, templateVariable };
  };

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div>
      <VariableTreeView
        templateId={templateId}
        onVariableSelect={(variableName) => {
          const variables = getVariablesByName(variableName);
          console.log('Selected variable instances:', variables);
        }}
      />

      {hasChanges && (
        <button
          onClick={saveChanges}
          disabled={isSaving}
        >
          {isSaving ? 'Saving...' : 'Save Changes'}
        </button>
      )}
    </div>
  );
};

export default MyVariableComponent;
```

## Behobene Bugs und Verbesserungen

### Bug Fix: Dataset ID Context Handling (2024-12)

**Problem:** Bei der Bearbeitung von Dataset-Variablen wurde die falsche ID verwendet.
- Dataset ID 41 ("mergetest") gehört zu Category ID 21 ("Sub-Kampagne1")
- Variable Tree verwendete Category ID (21) statt Dataset ID (41) für API-Aufrufe
- Führte zu falschen API-Endpunkt-Aufrufen: `/api/variable-tree/dataset/21` statt `/api/variable-tree/dataset/41`

**Root Cause:** In `VariableTreeView.tsx` wurde für Datasets `nodeId` (Category ID) statt `datasetId` (Dataset ID) verwendet.

**Fix:**
```typescript
// Vorher (fehlerhaft):
updateVariable(variableName, newValue, variable, nodeId, selectedNode.type);

// Nachher (korrekt):
const contextId = selectedNode.type === 'dataset' ? datasetId : nodeId;
updateVariable(variableName, newValue, variable, contextId, selectedNode.type);
```

**Datei:** `components/template/VariableTreeView.tsx`

### Bug Fix: Variable Data Structure Corruption (2024-12)

**Problem:** Dataset- und Category-Variable wurden mit falscher Datenstruktur gespeichert.
- Variablen wurden als separate Root-Level-Properties gespeichert
- Beispiel: `{"vars": [...], "GNSSES": ["G", "E", "R", "C"]}` (falsch)
- Korrekt: `{"vars": [{"name": "GNSSES", "data": ["G", "E", "R", "C"], ...}]}` (richtig)

**Root Cause:** Dataset/Category API-Endpunkte verwendeten direktes Merging statt Variable-Objekt-Updates.

**Fix:** Dataset- und Category-APIs verwenden jetzt dieselbe Logik wie Template-API:
```typescript
// Vorher (fehlerhaft):
const updatedOverrides = {
  ...currentOverrides,
  ...variableOverrides  // Erstellt Root-Level-Properties
};

// Nachher (korrekt):
const updatedVarsList = currentVarsList.map((variable: any) => {
  if (variableOverrides.hasOwnProperty(variable.name)) {
    return {
      ...variable,
      data: variableOverrides[variable.name]  // Aktualisiert data-Feld
    };
  }
  return variable;
});

const updatedOverrides = {
  ...currentOverrides,
  vars: updatedVarsList  // Erhält korrekte Struktur
};
```

**Dateien:**
- `app/api/variable-tree/dataset/[id]/route.ts`
- `app/api/variable-tree/category/[id]/route.ts`

### Bug Fix: Variable Input Responsiveness (2024-12)

**Problem:** Variable-Input-Komponenten reagierten nicht auf Benutzerinteraktionen.
- Checkboxes und andere Inputs zeigten keine visuellen Updates
- Werte wurden nicht in der State-Management-Schicht aktualisiert

**Root Cause:** `VariableInputRenderer` verwendete ursprüngliche Werte statt aktuelle Werte aus State-Management.

**Fix:**
```typescript
// Vorher (fehlerhaft):
<InputRenderer
  variable={{
    ...templateVariable,
    data: variable.value || variable.data  // Immer ursprünglicher Wert
  }}
/>

// Nachher (korrekt):
<InputRenderer
  variable={templateVariable}  // templateVariable enthält bereits currentValue
/>
```

**Dateien:**
- `components/template/VariableInputRenderer.tsx`
- `components/template/VariableTreeView.tsx`

### Bug Fix: Duplicate Save/Reset Buttons (2024-12)

**Problem:** Save- und Reset-Buttons erschienen doppelt im UI-Header.

**Root Cause:** Zwei separate `{hasChanges && (...)}` Blöcke in `VariableTreeView`.

**Fix:** Entfernung des duplizierten Blocks.

**Datei:** `components/template/VariableTreeView.tsx`

### Bug Fix: URL Parameter Support (2024-12)

**Problem:** Variable Tree Page ignorierte `templateId` URL-Parameter.

**Fix:** Hinzufügung von `useSearchParams` für URL-Parameter-Unterstützung.

**Datei:** `app/variable-tree/page.tsx`

## Testing

### Unit Tests
- `useVariableTree` Hook-Funktionen
- Variable-Override-Logik
- GUI-Komponenten-Rendering

### Integration Tests
- API-Endpunkt mit verschiedenen Template-IDs
- Vollständiger Datenfluss: DB → API → UI
- Override-Szenarien testen

### Regression Tests für Bug Fixes
- Dataset ID Context: Teste Variable-Änderungen in verschiedenen Datasets
- Data Structure: Verifiziere korrekte Variable-Objekt-Struktur nach Updates
- Input Responsiveness: Teste Checkbox/Input-Interaktionen
- URL Parameters: Teste direkte Navigation zu spezifischen Templates
