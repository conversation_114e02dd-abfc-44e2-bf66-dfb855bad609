# Frontend Variable Tree View - Entwicklungsanleitung

## Überblick

Diese Anleitung beschreibt die Entwicklung einer hierarchischen Baumansicht für die Variablen-Verwaltung in AlgoNav. Das System ermöglicht es Benutzern, Variablen auf verschiedenen Ebenen (Template, Kategorie, Dataset) zu setzen und zu überschreiben, mit visueller Darstellung der Vererbungshierarchie.

## Verfügbare APIs

### 1. Tree API (`/api/tree`)

**Endpoint:** `GET /api/tree?withCounts=true`

**Zweck:** Liefert die hierarchische Struktur aller Kategorien mit optionalen Dataset-Zählungen.

**Response Format:**
```typescript
interface TreeNode {
  id: number;
  parentId: number | null;
  name: string;
  path: string;  // ltree format: "1.2.3"
  level: number; // Tiefe im Baum
  datasetCnt?: number; // Anzahl Datasets (wenn withCounts=true)
}
```

**Verwendung:**
```typescript
const treeData = await api.getTreeSkeleton(true);
```

### 2. Var-Nodes API (`/api/var-nodes`)

**Endpoint:** `GET /api/var-nodes?key={variableName}&datasetId={id}&templateId={id}`

**Zweck:** Zeigt alle Stellen im Baum, wo eine bestimmte Variable gesetzt ist, mit Markierung der aktiven Variable.

**Response Format:**
```typescript
interface VarNode {
  id: number;
  path: string;
  kind: 'cat' | 'ds' | 'tpl'; // category, dataset, template
  is_active: boolean; // true für die Variable mit höchster Priorität
}
```

**Prioritätsreihenfolge:**
1. Dataset (höchste Priorität)
2. Kategorie (tiefere Kategorien überschreiben oberflächlichere)
3. Template (niedrigste Priorität)

### 3. Variable Merge API (RPC-Funktion)

**Funktion:** `merge_job_vars(template_id, dataset_ids[])`

**Zweck:** Merged Variablen aus Template, Kategorien und Datasets nach Priorität.

## Bestehende Komponenten

### Variable Editor
- **Pfad:** `components/template/InputRenderer.tsx`
- **Zweck:** Rendert verschiedene GUI-Komponenten für Variablen
- **Store:** `lib/stores/templateStore`
- **Typen:** `components/template/types/template.ts`

### Variable Types
```typescript
interface TemplateVariable {
  name: string;
  links: any;
  data: any;
  component_name?: string;
  gui?: {
    component_id: string;
    group: string;
    order: number;
    label?: string;
    tooltip?: string;
    // ... weitere GUI-Eigenschaften
  };
}
```

## UI/UX Konzept

### Layout-Struktur

```
┌─────────────────────────────────────────────────────────────┐
│ Template Dropdown: [GNSS Post Processing ▼]                │
├─────────────────────┬───────────────────────────────────────┤
│ Tree View (Links)   │ Variable Editor (Rechts)              │
│                     │                                       │
│ 📁 Kampagne 1       │ Variables for: Dataset XYZ            │
│ ├─ 🚗 Fahrzeug A    │ ┌─────────────────────────────────────┐ │
│ │  ├─ 📊 Dataset 1  │ │ LEVERARM_X: [1.234] (from Vehicle) │ │
│ │  └─ 📊 Dataset 2  │ │ LEVERARM_Y: [0.567] (from Dataset) │ │
│ └─ 🚗 Fahrzeug B    │ │ BASE_STATION: [AUTO] (from Template)│ │
│    └─ 📊 Dataset 3  │ └─────────────────────────────────────┘ │
│                     │                                       │
│ Legende:            │ [Save Changes] [Reset to Template]   │
│ 🟢 Aktive Variable  │                                       │
│ 🟡 Überschrieben    │                                       │
│ ⚪ Template Default │                                       │
│ 🔴 Erforderlich ✕   │                                       │
└─────────────────────┴───────────────────────────────────────┘
```

### Interaktionskonzept

#### 1. Template-Auswahl
- Dropdown mit verfügbaren Templates
- Lädt Baum und Standard-Variablen neu

#### 2. Baum-Navigation
- **Kategorie-Klick:** Zeigt aggregierte Variablen aller Unter-Datasets
- **Dataset-Klick:** Zeigt spezifische Dataset-Variablen
- **Visuelle Indikatoren:**
  - 🟢 Grün: Variable ist hier aktiv (höchste Priorität)
  - 🟡 Gelb: Variable ist hier gesetzt, aber überschrieben
  - ⚪ Grau: Nur Template-Default

#### 3. Variable-Editor
- Wiederverwendung der bestehenden `InputRenderer` Komponente
- Anzeige der Quelle jeder Variable
- Möglichkeit, Variablen auf aktueller Ebene zu überschreiben

### Constraints & Required

- Ebenen: `Global = 0`, `Kategorien = 1..N` (Tiefe), `Dataset = 999`.
- `variable.gui.constraints.minLevel/maxLevel` steuern die Editierbarkeit pro Ebene.
  - Inputs sind deaktiviert, wenn die aktuelle Ebene außerhalb des erlaubten Bereichs liegt.
  - „Override/Specify“ wird nur angeboten, wenn die aktuelle Ebene erlaubt ist.
- `variable.gui.required` kennzeichnet erforderliche Variablen.
  - Wenn entlang der Vererbung kein effektiver Wert existiert, zeigt die UI einen roten Zustand und priorisiert die Anzeige auf Global.

## Implementierungsplan

### Phase 1: Basis-Komponenten

#### 1.1 Tree Component
```typescript
interface TreeViewProps {
  templateId: number;
  selectedNodeId?: number;
  highlightedVariables?: string[]; // Variable names to highlight
  onNodeSelect: (nodeId: number, nodeType: 'category' | 'dataset') => void;
}

const TreeView: React.FC<TreeViewProps> = ({ ... }) => {
  // Implementierung mit MUI TreeView oder custom component
};
```

#### 1.2 Variable Hierarchy Display
```typescript
interface VariableHierarchyProps {
  variableName: string;
  templateId: number;
  datasetId?: number;
  onVariableChange: (name: string, value: any, level: 'template' | 'category' | 'dataset') => void;
}

const VariableHierarchy: React.FC<VariableHierarchyProps> = ({ ... }) => {
  // Zeigt Variable-Vererbung und ermöglicht Überschreibung
};
```

### Phase 2: Integration

#### 2.1 Main Container
```typescript
const VariableTreeView: React.FC = () => {
  const [selectedTemplate, setSelectedTemplate] = useState<number>();
  const [selectedNode, setSelectedNode] = useState<{id: number, type: 'category' | 'dataset'}>();
  const [activeVariable, setActiveVariable] = useState<string>();
  
  // State management und API calls
};
```

#### 2.2 State Management
- Erweitern des bestehenden `templateStore` oder neuer `variableTreeStore`
- Caching von Tree-Daten und Variable-Zuständen

### Phase 3: Erweiterte Features

#### 3.1 Variable Impact Analysis
- Zeige, welche Datasets von einer Variable-Änderung betroffen wären
- "What-if" Analyse vor dem Speichern

#### 3.2 Bulk Operations
- Mehrere Variablen gleichzeitig setzen
- Copy/Paste von Variable-Sets zwischen Ebenen

#### 3.3 Conflict Resolution
- Warnung bei Variable-Konflikten
- Vorschläge für optimale Variable-Platzierung

## Technische Überlegungen

### Performance
- **Lazy Loading:** Lade nur sichtbare Baum-Teile
- **Virtualization:** Für große Bäume mit vielen Datasets
- **Debounced API Calls:** Bei Variable-Änderungen

### Datenstrukturen
```typescript
// Erweiterte Tree Node für UI
interface UITreeNode extends TreeNode {
  children?: UITreeNode[];
  variableIndicators?: {
    [variableName: string]: 'active' | 'overridden' | 'template';
  };
  isExpanded?: boolean;
  isSelected?: boolean;
}

// Variable mit Kontext
interface ContextualVariable extends TemplateVariable {
  source: {
    type: 'template' | 'category' | 'dataset';
    id: number;
    name: string;
    path?: string;
  };
  isActive: boolean;
  isOverridden: boolean;
  overriddenBy?: {
    type: 'category' | 'dataset';
    id: number;
    name: string;
  }[];
}
```

### Error Handling
- Graceful degradation bei API-Fehlern
- Offline-Modus mit lokaler State-Verwaltung
- Validation vor dem Speichern

## Benutzerfreundlichkeit

### Guided Experience
1. **Onboarding:** Tutorial für neue Benutzer
2. **Tooltips:** Erklärung der Vererbungsregeln
3. **Visual Feedback:** Sofortige Anzeige von Änderungsauswirkungen

### Accessibility
- Keyboard-Navigation im Baum
- Screen Reader Support
- High Contrast Mode

## Nächste Schritte

1. **Prototyping:** Erstelle einen einfachen Prototyp mit statischen Daten
2. **API Integration:** Verbinde mit den bestehenden APIs
3. **User Testing:** Teste das Konzept mit echten Benutzern
4. **Iterative Verbesserung:** Basierend auf Feedback

## Code-Beispiele

### API Usage
```typescript
// Tree laden
const tree = await api.getTreeSkeleton(true);

// Variable-Nodes für spezifische Variable
const varNodes = await api.getVarNodes('LEVERARM_X', datasetId, templateId);

// Merged Variables für Job
const mergedVars = await supabase.rpc('merge_job_vars', {
  p_template_id: templateId,
  p_dataset_ids: [datasetId]
});
```

### Component Integration
```typescript
// Bestehenden Variable Editor wiederverwenden
<InputRenderer 
  variable={contextualVariable} 
  template_data={templateData}
/>

// Mit Kontext-Information erweitern
<VariableSourceIndicator 
  source={variable.source}
  isActive={variable.isActive}
  overriddenBy={variable.overriddenBy}
/>
```

Diese Anleitung bietet eine solide Grundlage für die Entwicklung der hierarchischen Variablen-Baumansicht und kann als Briefing für weitere Entwicklungsarbeiten verwendet werden.
