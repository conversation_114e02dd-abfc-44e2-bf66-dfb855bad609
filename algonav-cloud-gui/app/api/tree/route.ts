import { withAuth } from '@/lib/api/withAuth';
import { NextResponse } from 'next/server';
import { createClient } from "@/utils/supabase/server";

export const GET = withAuth(async (userId, request) => {
    const supabase = createClient();
    const { searchParams } = new URL(request.url);
    const withCounts = searchParams.get('withCounts') === 'true';

    try {
        // Try to use RPC function first, fallback to direct query
        const { data: rpcData, error: rpcError } = await supabase.rpc('get_tree_skeleton', { 
            with_counts: withCounts 
        });

        if (!rpcError && rpcData) {
            return NextResponse.json({ success: true, data: rpcData });
        }

        // Fallback to direct query if RPC doesn't exist
        if (withCounts) {
            // Query with dataset counts
            const { data, error } = await supabase
                .from('categories')
                .select(`
                    id,
                    parent_category_id,
                    name,
                    path,
                    datasets(count)
                `)
                .eq('user_id', userId)
                .order('path');

            if (error) {
                return NextResponse.json({ error: error.message }, { status: 500 });
            }

            const transformedData = data?.map(category => ({
                id: category.id,
                parentId: category.parent_category_id,
                name: category.name,
                path: category.path,
                level: category.path ? category.path.toString().split('.').length : 1,
                datasetCnt: category.datasets?.[0]?.count || 0
            })) || [];

            return NextResponse.json({ success: true, data: transformedData });
        } else {
            // Query without dataset counts
            const { data, error } = await supabase
                .from('categories')
                .select('id, parent_category_id, name, path')
                .eq('user_id', userId)
                .order('path');

            if (error) {
                return NextResponse.json({ error: error.message }, { status: 500 });
            }

            const transformedData = data?.map(category => ({
                id: category.id,
                parentId: category.parent_category_id,
                name: category.name,
                path: category.path,
                level: category.path ? category.path.toString().split('.').length : 1
            })) || [];

            return NextResponse.json({ success: true, data: transformedData });
        }

    } catch (error) {
        console.error('Tree endpoint error:', error);
        return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
    }
});
