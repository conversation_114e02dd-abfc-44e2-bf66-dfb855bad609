import { withAuth } from '@/lib/api/withAuth';
import { NextResponse } from 'next/server';
import { createClient } from "@/utils/supabase/server";

export const POST = withAuth(async (userId, request) => {
    const supabase = createClient();
    
    try {
        const { templateId, datasetIds } = await request.json();

        // Validate required fields
        if (!templateId || typeof templateId !== 'number' || templateId <= 0) {
            return NextResponse.json({ error: 'Valid templateId is required' }, { status: 400 });
        }

        if (!datasetIds || !Array.isArray(datasetIds) || datasetIds.length === 0) {
            return NextResponse.json({ error: 'datasetIds array is required and cannot be empty' }, { status: 400 });
        }

        // Validate all dataset IDs are positive numbers
        if (!datasetIds.every(id => typeof id === 'number' && id > 0)) {
            return NextResponse.json({ error: 'All dataset IDs must be positive numbers' }, { status: 400 });
        }

        // Check for duplicate dataset IDs
        if (new Set(datasetIds).size !== datasetIds.length) {
            return NextResponse.json({ error: 'Dataset IDs must be unique' }, { status: 400 });
        }

        // Try to use RPC function first
        const { data: rpcData, error: rpcError } = await supabase.rpc('merge_job_vars', {
            p_template_id: templateId,
            p_dataset_ids: datasetIds
        });

        if (!rpcError && rpcData) {
            return NextResponse.json({ success: true, data: rpcData });
        }

        return NextResponse.json({ error: 'RPC function not found or RPC execution failed: ' + (rpcError?.message || 'Unknown error') }, { status: 400 });

        

    } catch (error) {
        console.error('Job effective endpoint error:', error);
        return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
    }
});
