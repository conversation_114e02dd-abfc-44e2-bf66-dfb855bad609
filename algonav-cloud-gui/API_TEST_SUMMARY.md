# API Test Summary

## Setup für API-Key Authentifizierung

Für Tests ohne Supabase-Authentifizierung können folgende Umgebungsvariablen gesetzt werden:

```bash
# In .env.local oder als Umgebungsvariablen
API_KEY=your-secret-test-api-key-here
TEST_USER_ID=6c62d0d6-9ee0-4952-a2b9-6506de27ee54  # Optional, verwendet bestehende User-ID als Default
```

Die `withAuth` Funktion prüft zuerst auf den `x-api-key` Header, bevor sie auf Supabase-Authentifizierung zurückfällt.

**Wichtig**: Nach dem Setzen der Umgebungsvariablen muss die Next.js-Anwendung neu gestartet werden:

```bash
# Next.js Entwicklungsserver neu starten
cd algonav-cloud-gui
# Stoppe den aktuellen Server (Ctrl+C) und starte neu:
npm run dev
```

**Schnelltest mit Node.js:**
```bash
# Test-Script ausführen (während Next.js läuft)
node test-api-key.js
```

## Implementierte Endpunkte

### 1. `/api/tree` (GET)
**Zweck**: Liefert flachen Kategorie-Baum mit optionalen Dataset-Counts

**CURL Test (mit API-Key):**
```bash
curl -X GET "http://localhost:3000/api/tree?withCounts=true" \
  -H "Content-Type: application/json" \
  -H "x-api-key: your-test-api-key"
```

**CURL Test (mit Session Cookie):**
```bash
curl -X GET "http://localhost:3000/api/tree?withCounts=true" \
  -H "Content-Type: application/json" \
  -H "Cookie: your-session-cookie"
```

**Erwartete Antwort:**
```json
{
  "success": true,
  "data": [
    {
      "id": 18,
      "parentId": null,
      "name": "Campaign_2024",
      "path": "18",
      "level": 1,
      "datasetCnt": 0
    },
    {
      "id": 19,
      "parentId": null,
      "name": "Indoor_Navigation", 
      "path": "19",
      "level": 1,
      "datasetCnt": 0
    },
    {
      "id": 21,
      "parentId": 19,
      "name": "Building_A",
      "path": "19.21", 
      "level": 2,
      "datasetCnt": 1
    }
  ]
}
```

### 2. `/api/var-nodes` (GET)
**Zweck**: Findet alle Knoten (Kategorien/Datasets/Templates), die einen bestimmten Variable-Key enthalten

**Parameter:**
- `key` (required): Variable-Name zu suchen
- `datasetId` (optional): Dataset-Kontext für Active-Status-Markierung

**CURL Test (ohne Dataset-Kontext):**
```bash
curl -X GET "http://localhost:3000/api/var-nodes?key=ENVIRONMENT" \
  -H "Content-Type: application/json" \
  -H "x-api-key: your-test-api-key"
```

**Erwartete Antwort:**
```json
{
  "success": true,
  "data": [
    {
      "id": 20,
      "path": "20",
      "kind": "cat",
      "is_active": false
    }
  ]
}
```

**CURL Test (mit Dataset-Kontext für Active-Markierung):**
```bash
curl -X GET "http://localhost:3000/api/var-nodes?key=category1&datasetId=41" \
  -H "Content-Type: application/json" \
  -H "x-api-key: your-test-api-key"
```

**Erwartete Antwort (mit Active-Markierung):**
```json
{
  "success": true,
  "data": [
    {
      "id": 21,
      "path": "19.21",
      "kind": "cat",
      "is_active": true
    },
    {
      "id": 19,
      "path": "19",
      "kind": "cat",
      "is_active": false
    },
    {
      "id": 8,
      "path": "template",
      "kind": "tpl",
      "is_active": false
    }
  ]
}
```

**Test für PROCESSING_FROM (findet Datasets + Templates):**
```bash
curl -X GET "http://localhost:3000/api/var-nodes?key=PROCESSING_FROM" \
  -H "Content-Type: application/json" \
  -H "x-api-key: your-test-api-key"
```

**Test für Template-Variablen:**
```bash
curl -X GET "http://localhost:3000/api/var-nodes?key=GNSSES" \
  -H "Content-Type: application/json" \
  -H "x-api-key: your-test-api-key"
```

**Erwartete Antwort (Templates):**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "path": "template",
      "kind": "tpl"
    },
    {
      "id": 10,
      "path": "template",
      "kind": "tpl"
    }
  ]
}
```

### 3. `/api/job/effective` (POST)
**Zweck**: Batch-Merge von Template + N Datasets für effektive Variablen

**CURL Test (mit API-Key):**
```bash
curl -X POST "http://localhost:3000/api/job/effective" \
  -H "Content-Type: application/json" \
  -H "x-api-key: your-test-api-key" \
  -d '{
    "templateId": 1,
    "datasetIds": [15, 17]
  }'
```

**Erwartete Antwort:**
```json
{
  "success": true,
  "data": [
    {
      "datasetId": 15,
      "vars": {
        "vars": [...], // Template vars from global_job_templates
        "BUILDING_ID": "A", // From category hierarchy
        "ENVIRONMENT": "indoor",
        "FLOOR_COUNT": 3,
        "GPS_AVAILABLE": false,
        "BUILDING_FLOOR": 1, // From dataset overrides
        "PROCESSING_FROM": [100],
        "PROCESSING_UNTIL": [200]
      }
    },
    {
      "datasetId": 17,
      "vars": {
        "vars": [...], // Template vars
        "BUILDING_ID": "B", // From category hierarchy  
        "ENVIRONMENT": "indoor",
        "FLOOR_COUNT": 5,
        "GPS_AVAILABLE": false,
        "BUILDING_FLOOR": 2, // From dataset overrides
        "PROCESSING_FROM": [300],
        "PROCESSING_UNTIL": [400]
      }
    }
  ]
}
```

## Testdaten in der Datenbank

### Kategorien:
- **Campaign_2024** (ID: 18, path: "18") - Root-Kategorie
- **Indoor_Navigation** (ID: 19, path: "19") - Root-Kategorie mit ENVIRONMENT: "indoor"
- **Outdoor_Navigation** (ID: 20, path: "20") - Root-Kategorie mit ENVIRONMENT: "outdoor"
- **Building_A** (ID: 21, path: "19.21") - Subkategorie mit BUILDING_ID: "A"
- **Building_B** (ID: 22, path: "19.22") - Subkategorie mit BUILDING_ID: "B"
- **Highway_Tests** (ID: 23, path: "20.23") - Subkategorie mit ROAD_TYPE: "highway"

### Datasets:
- **268a** (ID: 15) - Zugeordnet zu Building_A, mit PROCESSING_FROM/UNTIL overrides
- **268b** (ID: 17) - Zugeordnet zu Building_B, mit PROCESSING_FROM/UNTIL overrides

### Templates:
- **test** (ID: 1) - Template mit GNSSES-Konfiguration
- **tightly** (ID: 10) - Umfangreiches Template mit vielen Variablen

## RPC-Funktionen

### `get_tree_skeleton(with_counts boolean)`
- Optimierte Tree-Abfrage mit optionalen Dataset-Counts
- Berechnet Level aus ltree path mit `nlevel()`

### `merge_job_vars(p_template_id int, p_dataset_ids int[])`
- Merge-Hierarchie: Template → Category-Chain → Dataset
- Verwendet `global_job_templates` (korrigiert von `job_templates`)
- Berücksichtigt ltree-Hierarchie für Category-Overrides

## Client-seitige Integration

### API-Funktionen (`lib/services/api.ts`):
- `getTreeSkeleton(withCounts: boolean)`
- `getVarNodes(key: string)`
- `getJobEffectiveVars(templateId: number, datasetIds: number[])`

### React Hooks (`lib/hooks/useTree.ts`):
- `useTreeSkeleton(withCounts: boolean)`
- `useVarNodes(key: string, enabled: boolean)`
- `useJobEffectiveVars(templateId: number, datasetIds: number[], enabled: boolean)`

## Fehlerbehandlung

Alle Endpunkte folgen dem bestehenden Pattern:
- **Erfolg**: `{ success: true, data: ... }`
- **Fehler**: `{ error: "message" }` mit entsprechendem HTTP-Status
- **Validierung**: 400 für ungültige Parameter
- **Auth**: 401 für fehlende Authentifizierung
- **Server**: 500 für Datenbankfehler

## Migration

Die Datenbankänderungen sind in der Migration-Datei dokumentiert:
```
supabase/migrations/20250804130000_create_tree_api_functions.sql
```

**Migration ausführen:**
```bash
cd algonav-cloud-gui
supabase db reset  # Für lokale Entwicklung
# oder
supabase migration up  # Für Produktion
```

**Enthaltene Änderungen:**
- `get_tree_skeleton(with_counts)` - Tree API RPC-Funktion
- `get_categories_with_key(key, user_id)` - Category-Suche für Var-Nodes API
- `get_datasets_with_key(key, user_id)` - Dataset-Suche für Var-Nodes API
- `get_templates_with_key(key)` - Template-Suche für Var-Nodes API
- `get_var_nodes_with_active_status(key, user_id, dataset_id)` - Enhanced Var-Nodes mit Active-Status-Markierung
- `merge_job_vars(template_id, dataset_ids[])` - Variable-Merge für Job Effective API
- Berechtigungen und Dokumentation für alle Funktionen
