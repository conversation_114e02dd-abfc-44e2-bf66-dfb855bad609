# Repository Guidelines

## Project Structure & Module Organization
- `app/`: Next.js App Router (routes, `app/api/*`, `layout.tsx`).
- `components/`: Reusable UI (tickets, dialogs, inputs, tables).
- `lib/`: Hooks, auth helpers, and services (see `lib/services/api.ts`).
- `utils/`: Shared utilities and Supabase clients (`utils/supabase/*`).
- `types/`: Shared TypeScript types. `public/`: static assets.
- `supabase/`: Database migrations/config. `__tests__/`: component/hook tests.
- `docs/` and product notes in `memory-bank/` support architecture/context.

## Build, Test, and Development Commands
- Install deps: `npm ci`
- Dev server: `npm run dev` (http://localhost:3000)
- Production build: `npm run build`; serve: `npm start`
- Type check: `npx tsc --noEmit`
- Tests: Tests live under `__tests__/` (Jest + React Testing Library). If not wired, install dev deps (e.g., `jest`, `@testing-library/react`, `@testing-library/jest-dom`, `ts-jest`, `@types/jest`) and run `npx jest`.

## Coding Style & Naming Conventions
- Language: TypeScript for new code; 2‑space indentation.
- Components: PascalCase `.tsx` in `components/`; hooks in `lib/hooks` prefixed `use*`.
- Routes: lowercase folders in `app/...`; API handlers in `app/api/*` wrapped with `lib/api/withAuth.ts`.
- Data access: prefer `lib/services/api.ts` over ad‑hoc `fetch` in components.
- UI: Tailwind utilities + MUI components; avoid inline styles except quick overrides.

## Testing Guidelines
- File names: `__tests__/components/Foo.test.tsx`, `__tests__/hooks/useBar.test.tsx`.
- Focus: render states, user events, error paths; mock Supabase via `utils/supabase/*` (see existing tests).
- Aim for coverage on core flows: tickets, templates, jobs; keep tests deterministic (no real network).

## Commit & Pull Request Guidelines
- Commits: Conventional Commits style used here (e.g., `feat(variable-tree): ...`, `refactor(...): ...`, `chore: ...`, `docs: ...`).
- PRs must include: clear description, linked issue (e.g., “Closes #123”), screenshots/gifs for UI, notes on env/migration changes, and manual test steps. Ensure `npm run build` and type‑checks pass.

## Security & Configuration Tips
- Env: `.env.local` with `NEXT_PUBLIC_SUPABASE_URL`, `NEXT_PUBLIC_SUPABASE_ANON_KEY`; optional `API_KEY`/`TEST_USER_ID` for dev auth in `withAuth`.
- Never commit secrets; keep API‑only logic server‑side; validate inputs in route handlers.

## Database
- you can use the supabase db MCP to read from the database to understand it better, this is more direct and better than reading the migrations files.
- for editing the database only use the migrations files and the supabase cli to apply them.
