-- Migration: Create get_variable_tree_with_context RPC function
-- This function retrieves the complete variable tree with context for efficient GUI rendering

CREATE OR REPLACE FUNCTION get_variable_tree_with_context(
    p_template_id INTEGER,
    p_user_id UUID
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    result JSONB;
    template_vars JSONB;
BEGIN
    -- Check if user has access to the template
    IF NOT EXISTS (
        SELECT 1 FROM global_job_templates 
        WHERE id = p_template_id AND user_id = p_user_id
    ) THEN
        RAISE EXCEPTION 'Template not found or access denied';
    END IF;

    -- Get template variables
    SELECT vars INTO template_vars
    FROM global_job_templates
    WHERE id = p_template_id;

    -- Build the complete tree structure with variable context
    WITH RECURSIVE category_tree AS (
        -- Base case: root categories for this user
        SELECT 
            c.id,
            c.name,
            c.description,
            c.parent_category_id,
            c.variable_overrides,
            c.path,
            0 as level,
            ARRAY[c.id] as path_array
        FROM categories c
        WHERE c.user_id = p_user_id 
        AND c.parent_category_id IS NULL
        
        UNION ALL
        
        -- Recursive case: child categories
        SELECT 
            c.id,
            c.name,
            c.description,
            c.parent_category_id,
            c.variable_overrides,
            c.path,
            ct.level + 1,
            ct.path_array || c.id
        FROM categories c
        INNER JOIN category_tree ct ON c.parent_category_id = ct.id
        WHERE c.user_id = p_user_id
    ),
    
    -- Get all datasets for each category
    datasets_with_vars AS (
        SELECT 
            d.id,
            d.name,
            d.description,
            d.category_id,
            d.variable_overrides,
            d.path
        FROM datasets d
        WHERE d.user_id = p_user_id
    ),
    
    -- Process variables for each node
    processed_tree AS (
        SELECT 
            ct.*,
            -- Process category variables
            CASE 
                WHEN ct.variable_overrides IS NOT NULL AND ct.variable_overrides ? 'vars' THEN
                    (
                        SELECT jsonb_agg(
                            jsonb_build_object(
                                'variable_name', var->>'name',
                                'source_level', 'Category',
                                'is_active', true,
                                'is_overridden', true,
                                'value', var->'data',
                                'gui_config', var->'gui',
                                'links', var->'links'
                            )
                        )
                        FROM jsonb_array_elements(ct.variable_overrides->'vars') AS var
                    )
                ELSE '[]'::jsonb
            END as category_variables,
            
            -- Get datasets for this category
            (
                SELECT jsonb_agg(
                    jsonb_build_object(
                        'id', d.id,
                        'name', d.name,
                        'description', d.description,
                        'type', 'dataset',
                        'variables', 
                        CASE 
                            WHEN d.variable_overrides IS NOT NULL AND d.variable_overrides ? 'vars' THEN
                                (
                                    SELECT jsonb_agg(
                                        jsonb_build_object(
                                            'variable_name', var->>'name',
                                            'source_level', 'Dataset',
                                            'is_active', true,
                                            'is_overridden', true,
                                            'value', var->'data',
                                            'gui_config', var->'gui',
                                            'links', var->'links'
                                        )
                                    )
                                    FROM jsonb_array_elements(d.variable_overrides->'vars') AS var
                                )
                            ELSE '[]'::jsonb
                        END
                    )
                )
                FROM datasets_with_vars d
                WHERE d.category_id = ct.id
            ) as datasets
        FROM category_tree ct
    )
    
    -- Build the final tree structure
    SELECT jsonb_build_object(
        'template_id', p_template_id,
        'template_variables', 
        CASE 
            WHEN template_vars IS NOT NULL AND template_vars ? 'vars' THEN
                (
                    SELECT jsonb_agg(
                        jsonb_build_object(
                            'variable_name', var->>'name',
                            'source_level', 'Template',
                            'is_active', true,
                            'is_overridden', false,
                            'value', var->'data',
                            'gui_config', var->'gui',
                            'links', var->'links'
                        )
                    )
                    FROM jsonb_array_elements(template_vars->'vars') AS var
                )
            ELSE '[]'::jsonb
        END,
        'tree', (
            WITH RECURSIVE build_tree AS (
                -- Start with root categories
                SELECT 
                    jsonb_build_object(
                        'id', pt.id,
                        'name', pt.name,
                        'description', pt.description,
                        'type', 'category',
                        'level', pt.level,
                        'variables', COALESCE(pt.category_variables, '[]'::jsonb),
                        'datasets', COALESCE(pt.datasets, '[]'::jsonb),
                        'children', '[]'::jsonb
                    ) as node,
                    pt.id,
                    pt.parent_category_id,
                    pt.level
                FROM processed_tree pt
                WHERE pt.parent_category_id IS NULL
                
                UNION ALL
                
                -- Add child categories
                SELECT 
                    jsonb_build_object(
                        'id', pt.id,
                        'name', pt.name,
                        'description', pt.description,
                        'type', 'category',
                        'level', pt.level,
                        'variables', COALESCE(pt.category_variables, '[]'::jsonb),
                        'datasets', COALESCE(pt.datasets, '[]'::jsonb),
                        'children', '[]'::jsonb
                    ) as node,
                    pt.id,
                    pt.parent_category_id,
                    pt.level
                FROM processed_tree pt
                INNER JOIN build_tree bt ON pt.parent_category_id = bt.id
            )
            SELECT jsonb_agg(node ORDER BY (node->>'name'))
            FROM build_tree
            WHERE parent_category_id IS NULL
        )
    ) INTO result;
    
    RETURN COALESCE(result, '{"template_id": null, "template_variables": [], "tree": []}'::jsonb);
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Error building variable tree: %', SQLERRM;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_variable_tree_with_context(INTEGER, UUID) TO authenticated;

-- Add helpful comment
COMMENT ON FUNCTION get_variable_tree_with_context(INTEGER, UUID) IS 
'Returns the complete variable tree structure with context for a given template and user. Includes template variables, category hierarchy, dataset variables, and variable inheritance information for efficient GUI rendering.';