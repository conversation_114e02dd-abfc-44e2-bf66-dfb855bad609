

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;


CREATE EXTENSION IF NOT EXISTS "pg_net" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgsodium";






COMMENT ON SCHEMA "public" IS 'standard public schema';



CREATE EXTENSION IF NOT EXISTS "ltree" WITH SCHEMA "public";






CREATE EXTENSION IF NOT EXISTS "pg_graphql" WITH SCHEMA "graphql";






CREATE EXTENSION IF NOT EXISTS "pg_stat_statements" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgcrypto" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgjwt" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "supabase_vault" WITH SCHEMA "vault";






CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA "extensions";






CREATE TYPE "public"."bulk_job_type" AS ENUM (
    'multi_kml'
);


ALTER TYPE "public"."bulk_job_type" OWNER TO "postgres";


CREATE TYPE "public"."ticket_priority" AS ENUM (
    'low',
    'medium',
    'high',
    'urgent'
);


ALTER TYPE "public"."ticket_priority" OWNER TO "postgres";


CREATE TYPE "public"."ticket_status" AS ENUM (
    'open',
    'in_progress',
    'waiting_on_customer',
    'resolved',
    'closed',
    'cancelled'
);


ALTER TYPE "public"."ticket_status" OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."create_dataset_with_files"("p_user_id" "uuid", "p_name" "text", "p_description" "text", "p_category_id" integer, "p_variable_overrides" "jsonb", "p_file_paths" "text"[]) RETURNS "json"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    v_dataset_id integer;
    v_file_path text;
    v_file_id integer;
    v_files_count integer := 0;
    v_associated_files_count integer := 0;
BEGIN
    -- Insert the dataset
    INSERT INTO public.datasets (user_id, name, description, category_id, variable_overrides)
    VALUES (p_user_id, p_name, p_description, p_category_id, p_variable_overrides)
    RETURNING id INTO v_dataset_id;

    -- Check if the dataset was actually inserted
    IF v_dataset_id IS NULL THEN
        RAISE EXCEPTION 'Failed to insert dataset';
    END IF;

    RAISE NOTICE 'Dataset created successfully with ID: %', v_dataset_id;

    -- Count the number of files provided
    v_files_count := array_length(p_file_paths, 1);

    -- Associate files with the dataset
    FOREACH v_file_path IN ARRAY p_file_paths LOOP
        -- Get the file ID from the file table, ensuring it belongs to the user
        SELECT id INTO v_file_id
        FROM public.files
        WHERE file_path = v_file_path AND user_id = p_user_id;

        IF v_file_id IS NOT NULL THEN
            INSERT INTO public.dataset_files (dataset_id, file_id)
            VALUES (v_dataset_id, v_file_id);
            v_associated_files_count := v_associated_files_count + 1;
            RAISE NOTICE 'File associated with dataset. Dataset ID: %, File ID: %', v_dataset_id, v_file_id;
        ELSE
            RAISE WARNING 'File not found or user does not have permission: %', v_file_path;
        END IF;
    END LOOP;

    RAISE NOTICE 'Associated % out of % files with the dataset', v_associated_files_count, v_files_count;

    RETURN json_build_object(
        'dataset_id', v_dataset_id,
        'associated_files_count', v_associated_files_count,
        'total_files_count', v_files_count
    );
EXCEPTION
    WHEN OTHERS THEN
        -- Log the error and re-raise
        RAISE NOTICE 'Error in create_dataset_with_files: %', SQLERRM;
        RAISE;
END;
$$;


ALTER FUNCTION "public"."create_dataset_with_files"("p_user_id" "uuid", "p_name" "text", "p_description" "text", "p_category_id" integer, "p_variable_overrides" "jsonb", "p_file_paths" "text"[]) OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."create_dataset_with_files"("p_user_id" "uuid", "p_name" "text", "p_description" "text", "p_category_id" integer, "p_variable_overrides" "jsonb", "p_file_paths" "text"[], "p_file_types" "text"[]) RETURNS "json"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    v_dataset_id integer;
    v_file_path text;
    v_file_id integer;
    v_file_type text;
    v_files_count integer := 0;
    v_associated_files_count integer := 0;
BEGIN
    -- Insert the dataset
    INSERT INTO public.datasets (user_id, name, description, category_id, variable_overrides)
    VALUES (p_user_id, p_name, p_description, p_category_id, p_variable_overrides)
    RETURNING id INTO v_dataset_id;

    -- Count the number of files provided
    v_files_count := array_length(p_file_paths, 1);

    -- Associate files with the dataset
    FOR i IN 1..v_files_count LOOP
        v_file_path := p_file_paths[i];
        v_file_type := p_file_types[i];

        -- Get the file ID from the file table, ensuring it belongs to the user
        SELECT id INTO v_file_id
        FROM public.files
        WHERE file_path = v_file_path AND user_id = p_user_id;

        IF v_file_id IS NOT NULL THEN
            INSERT INTO public.dataset_files (dataset_id, file_id, file_type)
            VALUES (v_dataset_id, v_file_id, v_file_type);
            v_associated_files_count := v_associated_files_count + 1;
        END IF;
    END LOOP;

    RETURN json_build_object(
        'dataset_id', v_dataset_id,
        'associated_files_count', v_associated_files_count,
        'total_files_count', v_files_count
    );
END;
$$;


ALTER FUNCTION "public"."create_dataset_with_files"("p_user_id" "uuid", "p_name" "text", "p_description" "text", "p_category_id" integer, "p_variable_overrides" "jsonb", "p_file_paths" "text"[], "p_file_types" "text"[]) OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_categories_with_key"("p_key" "text", "p_user_id" "uuid") RETURNS TABLE("id" integer, "path" "text")
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    RETURN QUERY
    SELECT c.id, c.path::text
    FROM categories c
    WHERE c.user_id = p_user_id 
      AND (
        -- Check direct key-value structure
        c.variable_overrides ? p_key
        OR
        -- Check vars array structure
        EXISTS (
          SELECT 1 
          FROM jsonb_array_elements(c.variable_overrides->'vars') AS var_item
          WHERE var_item->>'name' = p_key
        )
      );
END; $$;


ALTER FUNCTION "public"."get_categories_with_key"("p_key" "text", "p_user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_datasets_with_key"("p_key" "text", "p_user_id" "uuid") RETURNS TABLE("id" integer, "path" "text")
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    RETURN QUERY
    SELECT d.id, d.path::text
    FROM datasets d
    WHERE d.user_id = p_user_id 
      AND d.path IS NOT NULL  -- Only include datasets with paths
      AND (
        -- Check direct key-value structure
        d.variable_overrides ? p_key
        OR
        -- Check vars array structure
        EXISTS (
          SELECT 1 
          FROM jsonb_array_elements(d.variable_overrides->'vars') AS var_item
          WHERE var_item->>'name' = p_key
        )
      );
END; $$;


ALTER FUNCTION "public"."get_datasets_with_key"("p_key" "text", "p_user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_next_available_task"("p_table_name" "text" DEFAULT 'tasks'::"text", "p_worker_id" "text" DEFAULT NULL::"text") RETURNS TABLE("id" integer, "status" "text", "job_json" "jsonb", "vars" "jsonb", "workervars" "jsonb", "result" "jsonb", "created_at" timestamp with time zone, "updated_at" timestamp with time zone, "job_id" integer, "user_id" "uuid", "bulk_job_type" "text")
    LANGUAGE "plpgsql"
    AS $_$
DECLARE
    query TEXT;
    task_record RECORD;
BEGIN
    -- Dynamically construct the query to get and lock the next available task
    query := format('
        SELECT * FROM public.%I 
        WHERE status = ''queued'' 
        ORDER BY created_at ASC 
        LIMIT 1 
        FOR UPDATE SKIP LOCKED
    ', p_table_name);
    
    -- Execute the query and get the first available task
    FOR task_record IN EXECUTE query LOOP
        -- Update the task status to 'In Progress' immediately
        EXECUTE format('
            UPDATE public.%I 
            SET status = ''In Progress'', 
                updated_at = CURRENT_TIMESTAMP 
            WHERE id = $1
        ', p_table_name) USING task_record.id;
        
        -- Return the task data
        id := task_record.id;
        status := 'In Progress';
        job_json := task_record.job_json;
        vars := task_record.vars;
        workervars := task_record.workervars;
        result := task_record.result;
        created_at := task_record.created_at;
        updated_at := CURRENT_TIMESTAMP;
        job_id := task_record.job_id;
        user_id := task_record.user_id;
        bulk_job_type := task_record.bulk_job_type;
        
        RETURN NEXT;
        RETURN; -- Exit after finding one task
    END LOOP;
    
    -- If no task was found, return empty result
    RETURN;
END;
$_$;


ALTER FUNCTION "public"."get_next_available_task"("p_table_name" "text", "p_worker_id" "text") OWNER TO "postgres";


COMMENT ON FUNCTION "public"."get_next_available_task"("p_table_name" "text", "p_worker_id" "text") IS 'Gets the next available queued task and immediately locks it to prevent race conditions using SKIP LOCKED';



CREATE OR REPLACE FUNCTION "public"."get_or_create_notification_state"("p_user_id" "uuid") RETURNS TABLE("user_id" "uuid", "last_seen_at" timestamp with time zone, "created_at" timestamp with time zone, "updated_at" timestamp with time zone)
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    -- Try to get existing state
    RETURN QUERY
    SELECT ns.user_id, ns.last_seen_at, ns.created_at, ns.updated_at
    FROM user_notification_states ns
    WHERE ns.user_id = p_user_id;
    
    -- If no state exists, create one
    IF NOT FOUND THEN
        INSERT INTO user_notification_states (user_id)
        VALUES (p_user_id)
        RETURNING user_notification_states.user_id, user_notification_states.last_seen_at, 
                  user_notification_states.created_at, user_notification_states.updated_at
        INTO user_id, last_seen_at, created_at, updated_at;
        
        RETURN NEXT;
    END IF;
END;
$$;


ALTER FUNCTION "public"."get_or_create_notification_state"("p_user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_templates_with_key"("p_key" "text") RETURNS TABLE("id" integer, "path" "text")
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    RETURN QUERY
    SELECT jt.id, 'template' AS path
    FROM global_job_templates jt
    WHERE EXISTS (
        SELECT 1 
        FROM jsonb_array_elements(jt.vars->'vars') AS var_item
        WHERE var_item->>'name' = p_key
    );
END; $$;


ALTER FUNCTION "public"."get_templates_with_key"("p_key" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_tree_skeleton"("with_counts" boolean DEFAULT true) RETURNS TABLE("id" integer, "parentId" integer, "name" character varying, "path" "text", "level" integer, "datasetCnt" bigint)
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    IF with_counts THEN
        RETURN QUERY
        SELECT 
            c.id,
            c.parent_category_id as "parentId",
            c.name,
            c.path::text,
            nlevel(c.path) as level,
            COALESCE(COUNT(d.id), 0) as "datasetCnt"
        FROM categories c
        LEFT JOIN datasets d ON d.category_id = c.id
        GROUP BY c.id, c.parent_category_id, c.name, c.path
        ORDER BY c.path;
    ELSE
        RETURN QUERY
        SELECT 
            c.id,
            c.parent_category_id as "parentId",
            c.name,
            c.path::text,
            nlevel(c.path) as level,
            0::bigint as "datasetCnt"
        FROM categories c
        ORDER BY c.path;
    END IF;
END; $$;


ALTER FUNCTION "public"."get_tree_skeleton"("with_counts" boolean) OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_user_role"("user_id" "uuid" DEFAULT "auth"."uid"()) RETURNS "text"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    RETURN COALESCE(
        (SELECT raw_app_meta_data->>'role' FROM auth.users WHERE id = user_id),
        'user'
    );
END;
$$;


ALTER FUNCTION "public"."get_user_role"("user_id" "uuid") OWNER TO "postgres";


COMMENT ON FUNCTION "public"."get_user_role"("user_id" "uuid") IS 'Gets the role for a user from their app_metadata. Defaults to "user" if no role is set.';



CREATE OR REPLACE FUNCTION "public"."get_var_nodes_filtered"("p_key" "text", "p_user_id" "uuid", "p_dataset_id" integer DEFAULT NULL::integer, "p_template_id" integer DEFAULT NULL::integer) RETURNS TABLE("id" integer, "path" "text", "kind" "text", "is_active" boolean)
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    dataset_path ltree;
    active_source_type text;
    active_source_id int;
BEGIN
    -- If dataset_id is provided, get its path and determine active source
    IF p_dataset_id IS NOT NULL THEN
        SELECT d.path INTO dataset_path 
        FROM datasets d 
        WHERE d.id = p_dataset_id AND d.user_id = p_user_id;
        
        IF dataset_path IS NULL THEN
            RAISE EXCEPTION 'Dataset not found or access denied';
        END IF;
        
        -- Determine which source has the active variable (highest priority wins)
        -- Check dataset first (highest priority)
        IF EXISTS (
            SELECT 1 FROM datasets d
            WHERE d.id = p_dataset_id 
              AND d.user_id = p_user_id
              AND EXISTS (
                SELECT 1 
                FROM jsonb_array_elements(d.variable_overrides->'vars') AS var_item
                WHERE var_item->>'name' = p_key
              )
        ) THEN
            active_source_type := 'dataset';
            active_source_id := p_dataset_id;
        ELSE
            -- Check categories (by priority level - deepest wins)
            SELECT c.id INTO active_source_id
            FROM categories c
            WHERE c.path @> dataset_path
              AND c.user_id = p_user_id
              AND EXISTS (
                SELECT 1 
                FROM jsonb_array_elements(c.variable_overrides->'vars') AS var_item
                WHERE var_item->>'name' = p_key
              )
            ORDER BY nlevel(c.path) DESC
            LIMIT 1;
            
            IF active_source_id IS NOT NULL THEN
                active_source_type := 'category';
            ELSE
                -- Check template (lowest priority)
                IF p_template_id IS NOT NULL AND EXISTS (
                    SELECT 1 
                    FROM global_job_templates jt
                    WHERE jt.id = p_template_id
                      AND jt.user_id = p_user_id
                      AND EXISTS (
                        SELECT 1 
                        FROM jsonb_array_elements(jt.vars->'vars') AS var_item
                        WHERE var_item->>'name' = p_key
                      )
                ) THEN
                    active_source_type := 'template';
                    active_source_id := p_template_id;
                END IF;
            END IF;
        END IF;
    END IF;
    
    -- Return filtered nodes with active status
    RETURN QUERY
    -- Categories (only in the dataset's path if dataset_id provided)
    SELECT 
        c.id,
        c.path::text,
        'cat'::text as kind,
        (p_dataset_id IS NOT NULL AND active_source_type = 'category' AND c.id = active_source_id) as is_active
    FROM categories c
    WHERE c.user_id = p_user_id 
      AND EXISTS (
        SELECT 1 
        FROM jsonb_array_elements(c.variable_overrides->'vars') AS var_item
        WHERE var_item->>'name' = p_key
      )
      AND (p_dataset_id IS NULL OR c.path @> dataset_path) -- Filter by dataset path if provided
    
    UNION ALL
    
    -- Datasets (only the specific dataset if dataset_id provided)
    SELECT 
        d.id,
        d.path::text,
        'ds'::text as kind,
        (p_dataset_id IS NOT NULL AND active_source_type = 'dataset' AND d.id = active_source_id) as is_active
    FROM datasets d
    WHERE d.user_id = p_user_id 
      AND d.path IS NOT NULL
      AND EXISTS (
        SELECT 1 
        FROM jsonb_array_elements(d.variable_overrides->'vars') AS var_item
        WHERE var_item->>'name' = p_key
      )
      AND (p_dataset_id IS NULL OR d.id = p_dataset_id) -- Filter by specific dataset if provided
    
    UNION ALL
    
    -- Templates (only the specific template if template_id provided)
    SELECT 
        jt.id,
        jt.name::text, -- Use template name as path
        'tpl'::text as kind,
        (p_dataset_id IS NOT NULL AND active_source_type = 'template' AND jt.id = active_source_id) as is_active
    FROM global_job_templates jt
    WHERE jt.user_id = p_user_id
      AND EXISTS (
        SELECT 1 
        FROM jsonb_array_elements(jt.vars->'vars') AS var_item
        WHERE var_item->>'name' = p_key
      )
      AND (p_template_id IS NULL OR jt.id = p_template_id); -- Filter by specific template if provided
END; 
$$;


ALTER FUNCTION "public"."get_var_nodes_filtered"("p_key" "text", "p_user_id" "uuid", "p_dataset_id" integer, "p_template_id" integer) OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_var_nodes_with_active_status"("p_key" "text", "p_user_id" "uuid", "p_dataset_id" integer DEFAULT NULL::integer) RETURNS TABLE("id" integer, "path" "text", "kind" "text", "is_active" boolean)
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    dataset_path ltree;
    active_source_type text;
    active_source_id int;
BEGIN
    -- If dataset_id is provided, determine which source has the active variable
    IF p_dataset_id IS NOT NULL THEN
        SELECT d.path INTO dataset_path 
        FROM datasets d 
        WHERE d.id = p_dataset_id AND d.user_id = p_user_id;
        
        IF dataset_path IS NULL THEN
            RAISE EXCEPTION 'Dataset not found or access denied';
        END IF;
        
        -- Check dataset first (highest priority)
        IF EXISTS (
            SELECT 1 FROM datasets d
            WHERE d.id = p_dataset_id 
              AND d.user_id = p_user_id
              AND (
                d.variable_overrides ? p_key
                OR EXISTS (
                  SELECT 1 
                  FROM jsonb_array_elements(d.variable_overrides->'vars') AS var_item
                  WHERE var_item->>'name' = p_key
                )
              )
        ) THEN
            active_source_type := 'dataset';
            active_source_id := p_dataset_id;
        ELSE
            -- Check categories (by priority level - deepest wins)
            SELECT c.id INTO active_source_id
            FROM categories c
            WHERE c.path @> dataset_path
              AND c.user_id = p_user_id
              AND (
                c.variable_overrides ? p_key
                OR EXISTS (
                  SELECT 1 
                  FROM jsonb_array_elements(c.variable_overrides->'vars') AS var_item
                  WHERE var_item->>'name' = p_key
                )
              )
            ORDER BY nlevel(c.path) DESC
            LIMIT 1;
            
            IF active_source_id IS NOT NULL THEN
                active_source_type := 'category';
            ELSE
                -- Check templates (lowest priority)
                SELECT jt.id INTO active_source_id
                FROM global_job_templates jt
                WHERE EXISTS (
                    SELECT 1 
                    FROM jsonb_array_elements(jt.vars->'vars') AS var_item
                    WHERE var_item->>'name' = p_key
                )
                LIMIT 1; -- For now, just pick the first template
                
                IF active_source_id IS NOT NULL THEN
                    active_source_type := 'template';
                END IF;
            END IF;
        END IF;
    END IF;
    
    -- Return all nodes with active status
    RETURN QUERY
    -- Categories
    SELECT 
        c.id,
        c.path::text,
        'cat'::text as kind,
        (p_dataset_id IS NOT NULL AND active_source_type = 'category' AND c.id = active_source_id) as is_active
    FROM categories c
    WHERE c.user_id = p_user_id 
      AND (
        c.variable_overrides ? p_key
        OR EXISTS (
          SELECT 1 
          FROM jsonb_array_elements(c.variable_overrides->'vars') AS var_item
          WHERE var_item->>'name' = p_key
        )
      )
    
    UNION ALL
    
    -- Datasets  
    SELECT 
        d.id,
        d.path::text,
        'ds'::text as kind,
        (p_dataset_id IS NOT NULL AND active_source_type = 'dataset' AND d.id = active_source_id) as is_active
    FROM datasets d
    WHERE d.user_id = p_user_id 
      AND d.path IS NOT NULL
      AND (
        d.variable_overrides ? p_key
        OR EXISTS (
          SELECT 1 
          FROM jsonb_array_elements(d.variable_overrides->'vars') AS var_item
          WHERE var_item->>'name' = p_key
        )
      )
    
    UNION ALL
    
    -- Templates
    SELECT 
        jt.id,
        'template'::text,
        'tpl'::text as kind,
        (p_dataset_id IS NOT NULL AND active_source_type = 'template' AND jt.id = active_source_id) as is_active
    FROM global_job_templates jt
    WHERE EXISTS (
        SELECT 1 
        FROM jsonb_array_elements(jt.vars->'vars') AS var_item
        WHERE var_item->>'name' = p_key
    );
END; $$;


ALTER FUNCTION "public"."get_var_nodes_with_active_status"("p_key" "text", "p_user_id" "uuid", "p_dataset_id" integer) OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_variable_sources_by_dataset"("p_key" "text", "p_user_id" "uuid") RETURNS TABLE("dataset_id" integer, "dataset_name" "text", "dataset_path" "text", "source_id" integer, "source_name" "text", "source_path" "text", "source_kind" "text", "is_active" boolean)
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    RETURN QUERY
    SELECT 
        d.id as dataset_id,
        d.name as dataset_name,
        d.path::text as dataset_path,
        
        -- For now, just return the dataset itself as source
        d.id as source_id,
        d.name as source_name,
        d.path::text as source_path,
        'dataset'::text as source_kind,
        true as is_active
        
    FROM datasets d
    WHERE d.user_id = p_user_id 
      AND d.path IS NOT NULL
      AND (
        d.variable_overrides ? p_key
        OR EXISTS (
          SELECT 1 
          FROM jsonb_array_elements(d.variable_overrides->'vars') AS var_item
          WHERE var_item->>'name' = p_key
        )
      );
END; $$;


ALTER FUNCTION "public"."get_variable_sources_by_dataset"("p_key" "text", "p_user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."handle_storage_change"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    file_owner_id uuid;
    file_name text;
    folder_name text;
BEGIN
    -- Extract the first folder name from the file path
    folder_name := split_part(NEW.name, '/', 1);
    
    -- Check if the folder name is a valid UUID and exists in the auth.users table
    IF EXISTS (
        SELECT 1 FROM auth.users WHERE id::text = folder_name
    ) THEN
        file_owner_id := folder_name::uuid;
    ELSE
        -- If not a valid user UUID, set to NULL or handle as needed
        file_owner_id := NULL;
    END IF;

    -- Extract the file name from the path
    file_name := split_part(NEW.name, '/', -1);

    IF (TG_OP = 'INSERT') THEN
        INSERT INTO public.FILES (user_id, bucket_name, file_path, file_name, file_size, content_type)
        VALUES (
            file_owner_id,
            TG_ARGV[0]::text,
            NEW.name,
            file_name,
            COALESCE((NEW.metadata->>'size')::bigint, 0),
            COALESCE(NEW.metadata->>'mimetype', 'application/octet-stream')
        );
    ELSIF (TG_OP = 'DELETE') THEN
        DELETE FROM public.FILES WHERE bucket_name = TG_ARGV[0]::text AND file_path = OLD.name;
    END IF;

    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."handle_storage_change"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."is_admin"("user_id" "uuid" DEFAULT "auth"."uid"()) RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    RETURN COALESCE(
        (SELECT (raw_app_meta_data->>'role') = 'admin' FROM auth.users WHERE id = user_id),
        false
    );
END;
$$;


ALTER FUNCTION "public"."is_admin"("user_id" "uuid") OWNER TO "postgres";


COMMENT ON FUNCTION "public"."is_admin"("user_id" "uuid") IS 'Checks if a user has admin role. Defaults to current authenticated user.';



CREATE OR REPLACE FUNCTION "public"."merge_job_vars"("p_template_id" integer, "p_dataset_ids" integer[]) RETURNS TABLE("datasetId" integer, "vars" "jsonb")
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    RETURN QUERY
    WITH job AS (
        SELECT p_template_id AS template_id,
               UNNEST(p_dataset_ids) AS dataset_id
    ),
    all_vars AS (
        -- Template variables (priority 1000 - lowest)
        SELECT 
            job.dataset_id,
            var_item->>'name' AS var_name,
            var_item AS var_object,
            1000 AS priority
        FROM job
        LEFT JOIN global_job_templates jt ON jt.id = job.template_id
        CROSS JOIN LATERAL jsonb_array_elements(COALESCE(jt.vars->'vars', '[]'::jsonb)) AS var_item
        
        UNION ALL
        
        -- Category variables from vars array (priority 2000 + level - deeper categories win)
        SELECT 
            job.dataset_id,
            var_item->>'name' AS var_name,
            var_item AS var_object,
            2000 + nlevel(c.path) AS priority  -- Deeper categories have higher priority
        FROM job
        JOIN datasets d ON d.id = job.dataset_id
        JOIN categories c ON c.path @> d.path
        CROSS JOIN LATERAL jsonb_array_elements(c.variable_overrides->'vars') AS var_item
        WHERE c.variable_overrides ? 'vars'
        
        UNION ALL
        
        -- Category variables from simple key-value (priority 2000 + level)
        SELECT 
            job.dataset_id,
            kv.key AS var_name,
            jsonb_build_object(
                'name', kv.key,
                'data', kv.value,
                'links', '[]'::jsonb
            ) AS var_object,
            2000 + nlevel(c.path) AS priority  -- Deeper categories have higher priority
        FROM job
        JOIN datasets d ON d.id = job.dataset_id
        JOIN categories c ON c.path @> d.path
        CROSS JOIN LATERAL jsonb_each(c.variable_overrides) AS kv
        WHERE NOT (c.variable_overrides ? 'vars') AND kv.key != 'vars'
        
        UNION ALL
        
        -- Dataset variables (priority 9000 - highest)
        SELECT 
            job.dataset_id,
            var_item->>'name' AS var_name,
            var_item AS var_object,
            9000 AS priority
        FROM job
        JOIN datasets d ON d.id = job.dataset_id
        CROSS JOIN LATERAL jsonb_array_elements(COALESCE(d.variable_overrides->'vars', '[]'::jsonb)) AS var_item
    ),
    deduplicated_vars AS (
        SELECT 
            dataset_id,
            jsonb_agg(
                var_object 
                ORDER BY var_name
            ) AS vars_array
        FROM (
            SELECT DISTINCT ON (dataset_id, var_name)
                dataset_id,
                var_name,
                var_object,
                priority
            FROM all_vars
            WHERE var_name IS NOT NULL
            ORDER BY dataset_id, var_name, priority DESC  -- Higher priority wins
        ) unique_vars
        GROUP BY dataset_id
    )
    SELECT 
        dv.dataset_id as "datasetId",
        jsonb_build_object('vars', COALESCE(dv.vars_array, '[]'::jsonb)) AS vars
    FROM deduplicated_vars dv;
END; $$;


ALTER FUNCTION "public"."merge_job_vars"("p_template_id" integer, "p_dataset_ids" integer[]) OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."remove_json_comments"("commented_json" "text") RETURNS "json"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    json_without_comments TEXT := '';
    in_string BOOLEAN := FALSE;
    string_quote CHAR(1) := NULL;
    escape_char BOOLEAN := FALSE;
    in_comment BOOLEAN := FALSE;
    in_multiline_comment BOOLEAN := FALSE;
    i INTEGER := 1;
    c CHAR(1);
    next_c CHAR(1);
BEGIN
    WHILE i <= length(commented_json) LOOP
        c := substr(commented_json, i, 1);
        next_c := substr(commented_json, i+1, 1);
        
        -- Handle comments
        IF NOT in_string THEN
            IF c = '/' AND next_c = '/' THEN
                in_comment := TRUE;
                i := i + 2;
                CONTINUE;
            ELSIF c = '/' AND next_c = '*' THEN
                in_multiline_comment := TRUE;
                i := i + 2;
                CONTINUE;
            ELSIF in_comment AND c = E'\n' THEN
                in_comment := FALSE;
                i := i + 1;
                CONTINUE;
            ELSIF in_multiline_comment AND c = '*' AND next_c = '/' THEN
                in_multiline_comment := FALSE;
                i := i + 2;
                CONTINUE;
            ELSIF in_comment OR in_multiline_comment THEN
                i := i + 1;
                CONTINUE;
            END IF;
        END IF;

        -- Handle strings
        IF c IN ('"', '''') AND NOT escape_char THEN
            IF in_string AND c = string_quote THEN
                in_string := FALSE;
                string_quote := NULL;
            ELSIF NOT in_string THEN
                in_string := TRUE;
                string_quote := c;
            END IF;
        END IF;

        -- Handle escape characters
        IF c = '\' AND in_string THEN
            escape_char := NOT escape_char;
        ELSE
            escape_char := FALSE;
        END IF;

        -- Append character to result
        json_without_comments := format('%s%s', json_without_comments, c);
        i := i + 1;
    END LOOP;
    
    -- Return as JSON, preserving all whitespace
    RETURN json_without_comments::JSON;
EXCEPTION
    WHEN others THEN
        RAISE EXCEPTION 'Invalid JSON after removing comments. Processed string: %', json_without_comments;
END;
$$;


ALTER FUNCTION "public"."remove_json_comments"("commented_json" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."remove_json_comments_optimized"("commented_json" "text") RETURNS "json"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    result text := '';
    in_string BOOLEAN := FALSE;
    string_quote CHAR(1) := NULL;
    escape_char BOOLEAN := FALSE;
    in_comment BOOLEAN := FALSE;
    in_multiline_comment BOOLEAN := FALSE;
    i INTEGER := 1;
    c CHAR(1);
    next_c CHAR(1);
BEGIN
    -- Zeichenweise durch den JSON-String gehen
    WHILE i <= length(commented_json) LOOP
        c := substr(commented_json, i, 1);
        next_c := CASE WHEN i < length(commented_json) THEN substr(commented_json, i+1, 1) ELSE '' END;
        
        -- Kommentare behandeln - aber nur wenn wir nicht in einem String sind
        IF NOT in_string THEN
            IF c = '/' AND next_c = '/' THEN
                in_comment := TRUE;
                i := i + 2;
                CONTINUE;
            ELSIF c = '/' AND next_c = '*' THEN
                in_multiline_comment := TRUE;
                i := i + 2;
                CONTINUE;
            ELSIF in_comment AND c = E'\n' THEN
                in_comment := FALSE;
                result := result || c; -- Zeilenumbruch beibehalten
                i := i + 1;
                CONTINUE;
            ELSIF in_multiline_comment AND c = '*' AND next_c = '/' THEN
                in_multiline_comment := FALSE;
                i := i + 2;
                CONTINUE;
            ELSIF in_comment OR in_multiline_comment THEN
                i := i + 1;
                CONTINUE;
            END IF;
        END IF;
        
        -- Strings verarbeiten
        IF c IN ('"', '''') AND NOT escape_char THEN
            IF in_string AND c = string_quote THEN
                in_string := FALSE;
                string_quote := NULL;
            ELSIF NOT in_string THEN
                in_string := TRUE;
                string_quote := c;
            END IF;
        END IF;
        
        -- Escape-Zeichen behandeln
        IF c = '\' AND in_string THEN
            escape_char := NOT escape_char;
        ELSE
            escape_char := FALSE;
        END IF;
        
        -- Zeichen zum Ergebnis hinzufügen
        result := result || c;
        
        i := i + 1;
    END LOOP;
    
    -- Return as JSON, preserving all whitespace
    RETURN result::JSON;
EXCEPTION
    WHEN others THEN
        RAISE EXCEPTION 'Invalid JSON after removing comments. Error in processing.';
END;
$$;


ALTER FUNCTION "public"."remove_json_comments_optimized"("commented_json" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."set_task_status_with_check_and_lock"("taskid" integer, "oldstatus" "text", "newstatus" "text", "table_name" "text") RETURNS "text"
    LANGUAGE "plpgsql"
    AS $_$
DECLARE
    affected_rows TEXT;
    query TEXT;
BEGIN
    -- Dynamically construct the SELECT query with row locking
    query := format('SELECT status FROM public.%I WHERE id = $1 FOR NO KEY UPDATE NOWAIT', table_name);

    -- Execute the dynamic query
    EXECUTE query INTO affected_rows USING taskid;

    -- Check if the current status matches the expected old status
    IF affected_rows != oldstatus THEN
        RETURN 'Status mismatch';
    END IF;

    -- Dynamically construct the UPDATE query with explicit timestamp update
    -- This ensures timestamp is updated even if triggers are disabled
    query := format('UPDATE public.%I SET status = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2', table_name);

    -- Execute the dynamic UPDATE query
    EXECUTE query USING newstatus, taskid;

    RETURN 'Success';

EXCEPTION
    WHEN OTHERS THEN
        -- Return the exception message for debugging
        RETURN SQLERRM;
END;
$_$;


ALTER FUNCTION "public"."set_task_status_with_check_and_lock"("taskid" integer, "oldstatus" "text", "newstatus" "text", "table_name" "text") OWNER TO "postgres";


COMMENT ON FUNCTION "public"."set_task_status_with_check_and_lock"("taskid" integer, "oldstatus" "text", "newstatus" "text", "table_name" "text") IS 'Atomically updates task status with timestamp and status validation';



CREATE OR REPLACE FUNCTION "public"."set_user_role"("user_id" "uuid", "user_role" "text") RETURNS "void"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    -- Update the user's raw_app_meta_data with the role
    UPDATE auth.users 
    SET raw_app_meta_data = COALESCE(raw_app_meta_data, '{}'::jsonb) || jsonb_build_object('role', user_role)
    WHERE id = user_id;
END;
$$;


ALTER FUNCTION "public"."set_user_role"("user_id" "uuid", "user_role" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."sync_category_files"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    file_id_value INTEGER;
BEGIN
    SET search_path TO public;
    -- Verify ownership for each file_id in the variable_overrides
    FOR file_id_value IN 
        SELECT (file_id::text)::integer
        FROM jsonb_array_elements(NEW.variable_overrides->'workervars') AS elem,
             jsonb_array_elements_text(elem->'file_ids') AS file_id
    LOOP
        PERFORM verify_file_ownership(file_id_value, 'category', NEW.id);
    END LOOP;

    DELETE FROM public.category_files WHERE category_id = NEW.id;
  
    INSERT INTO public.category_files (category_id, file_id, file_type)
    SELECT 
        NEW.id,
        (file_id::text)::integer,
        jsonb_array_elements(NEW.variable_overrides->'workervars')->>'name'
    FROM jsonb_array_elements(NEW.variable_overrides->'workervars') AS elem,
         jsonb_array_elements_text(elem->'file_ids') AS file_id;
  
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."sync_category_files"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."sync_dataset_files"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    file_id_value INTEGER;
BEGIN
  SET search_path TO public;
    -- Verify ownership for each file_id in the variable_overrides
    FOR file_id_value IN 
        SELECT (file_id::text)::integer
        FROM jsonb_array_elements(NEW.variable_overrides->'workervars') AS elem,
             jsonb_array_elements_text(elem->'file_ids') AS file_id
    LOOP
        PERFORM verify_file_ownership(file_id_value, 'dataset', NEW.id);
    END LOOP;

    -- Delete old entries
    DELETE FROM dataset_files WHERE dataset_id = NEW.id;
  
    -- Insert new entries
    INSERT INTO dataset_files (dataset_id, file_id, file_type)
    SELECT 
        NEW.id,
        (file_id::text)::integer,
        jsonb_array_elements(NEW.variable_overrides->'workervars')->>'name'
    FROM jsonb_array_elements(NEW.variable_overrides->'workervars') AS elem,
         jsonb_array_elements_text(elem->'file_ids') AS file_id;
  
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."sync_dataset_files"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."sync_job_template_files"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    file_id INTEGER;
BEGIN
    SET search_path TO public;

    IF (TG_OP = 'INSERT' OR TG_OP = 'UPDATE') THEN
        -- Verify ownership for each file_id in the template_data
        FOR file_id IN 
            SELECT (jsonb_array_elements_text(elem->'file_ids'))::integer
            FROM jsonb_array_elements(NEW.template_data->'workervars') AS elem
        LOOP
            PERFORM verify_file_ownership(file_id, 'job_template', NEW.id);
        END LOOP;

        -- Delete existing entries and insert new ones
        DELETE FROM public.job_template_files WHERE job_template_id = NEW.id;
        
        INSERT INTO public.job_template_files (job_template_id, file_id, file_type)
        SELECT
            NEW.id,
            (jsonb_array_elements_text(elem->'file_ids'))::integer,
            elem->>'name'
        FROM jsonb_array_elements(NEW.template_data->'workervars') AS elem;

        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$;


ALTER FUNCTION "public"."sync_job_template_files"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."track_ticket_status_changes"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    -- Only insert if status actually changed
    IF OLD.status IS DISTINCT FROM NEW.status THEN
        INSERT INTO public.ticket_status_history (ticket_id, old_status, new_status, changed_by)
        VALUES (NEW.id, OLD.status, NEW.status, COALESCE(NEW.updated_by, NEW.creator_id));
    END IF;
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."track_ticket_status_changes"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."trg_set_path"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    parent_path ltree;
BEGIN
    -- Handle INSERT and UPDATE operations
    IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
        -- If this is a root category (no parent)
        IF NEW.parent_category_id IS NULL THEN
            NEW.path = NEW.id::text::ltree;
        ELSE
            -- Get parent's path
            SELECT path INTO parent_path 
            FROM categories 
            WHERE id = NEW.parent_category_id;
            
            -- If parent exists, build path
            IF parent_path IS NOT NULL THEN
                NEW.path = parent_path || NEW.id::text::ltree;
            ELSE
                -- Parent doesn't exist - this should not happen due to FK constraint
                RAISE EXCEPTION 'Parent category % does not exist', NEW.parent_category_id;
            END IF;
        END IF;
        
        -- Note: Descendant path updates are handled by a separate AFTER UPDATE trigger
        
        RETURN NEW;
    END IF;
    
    -- Should not reach here
    RETURN NULL;
END;
$$;


ALTER FUNCTION "public"."trg_set_path"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."trg_sync_dataset_path"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    -- Handle INSERT and UPDATE operations
    IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
        -- If dataset has a category, sync the path
        IF NEW.category_id IS NOT NULL THEN
            SELECT path INTO NEW.path 
            FROM categories 
            WHERE id = NEW.category_id;
        ELSE
            -- No category, clear the path
            NEW.path = NULL;
        END IF;
        
        RETURN NEW;
    END IF;
    
    -- Should not reach here
    RETURN NULL;
END;
$$;


ALTER FUNCTION "public"."trg_sync_dataset_path"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."trg_update_dataset_paths_on_category_change"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    -- Update all datasets in this category and its descendants
    UPDATE datasets 
    SET path = NEW.path
    WHERE category_id = NEW.id;
    
    -- If this was an update and the path changed, update descendant datasets too
    IF TG_OP = 'UPDATE' AND OLD.path IS DISTINCT FROM NEW.path THEN
        UPDATE datasets 
        SET path = c.path
        FROM categories c
        WHERE datasets.category_id = c.id 
        AND c.path <@ NEW.path;
    END IF;
    
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."trg_update_dataset_paths_on_category_change"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."trg_update_descendant_paths"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    -- Only run if parent_category_id changed
    IF OLD.parent_category_id IS DISTINCT FROM NEW.parent_category_id THEN
        -- Update all descendant paths
        PERFORM update_descendant_paths(NEW.id, OLD.path, NEW.path);
    END IF;
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."trg_update_descendant_paths"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_category_levels_updated_at"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_category_levels_updated_at"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_dataset_with_files"("p_dataset_id" integer, "p_description" "text", "p_file_paths" "text"[], "p_file_types" "text"[], "p_name" "text", "p_user_id" "uuid", "p_variable_overrides" "jsonb") RETURNS "json"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    v_file_path text;
    v_file_id integer;
    v_file_type text;
    v_rows_affected integer;
    v_dataset_exists boolean;
BEGIN
    -- Check if the dataset exists and belongs to the user
    SELECT EXISTS (
        SELECT 1 
        FROM public.datasets 
        WHERE id = p_dataset_id AND user_id = p_user_id
    ) INTO v_dataset_exists;

    IF NOT v_dataset_exists THEN
        RAISE EXCEPTION 'Dataset not found or user does not have permission to update it';
    END IF;

    -- Update the dataset
    UPDATE public.datasets
    SET name = p_name,
        description = p_description,
        variable_overrides = p_variable_overrides
    WHERE id = p_dataset_id AND user_id = p_user_id;

    GET DIAGNOSTICS v_rows_affected = ROW_COUNT;

    -- Remove existing file associations
    DELETE FROM public.dataset_files WHERE dataset_id = p_dataset_id;

    -- Add new file associations
    FOR i IN 1..array_length(p_file_paths, 1) LOOP
        v_file_path := p_file_paths[i];
        v_file_type := p_file_types[i];

        -- Check if the file exists and belongs to the user
        SELECT id INTO v_file_id
        FROM public.files
        WHERE file_path = v_file_path AND user_id = p_user_id;

        IF v_file_id IS NOT NULL THEN
            INSERT INTO public.dataset_files (dataset_id, file_id, file_type)
            VALUES (p_dataset_id, v_file_id, v_file_type);
        END IF;
    END LOOP;

    RETURN json_build_object('dataset_id', p_dataset_id);
END;
$$;


ALTER FUNCTION "public"."update_dataset_with_files"("p_dataset_id" integer, "p_description" "text", "p_file_paths" "text"[], "p_file_types" "text"[], "p_name" "text", "p_user_id" "uuid", "p_variable_overrides" "jsonb") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_dataset_with_files"("p_dataset_id" integer, "p_user_id" "uuid", "p_name" "text", "p_description" "text", "p_category_id" integer, "p_variable_overrides" "jsonb", "p_file_ids" integer[]) RETURNS "json"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  v_file_id integer;
BEGIN
  -- Update the dataset
  UPDATE public.datasets
  SET name = p_name,
      description = p_description,
      category_id = p_category_id,
      variable_overrides = p_variable_overrides
  WHERE id = p_dataset_id AND user_id = p_user_id;

  -- Remove existing file associations
  DELETE FROM public.dataset_files WHERE dataset_id = p_dataset_id;

  -- Add new file associations
  FOREACH v_file_id IN ARRAY p_file_ids LOOP
    INSERT INTO public.dataset_files (dataset_id, file_id)
    VALUES (p_dataset_id, v_file_id);
  END LOOP;

  RETURN json_build_object('dataset_id', p_dataset_id);
END;
$$;


ALTER FUNCTION "public"."update_dataset_with_files"("p_dataset_id" integer, "p_user_id" "uuid", "p_name" "text", "p_description" "text", "p_category_id" integer, "p_variable_overrides" "jsonb", "p_file_ids" integer[]) OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_dataset_with_files"("p_dataset_id" integer, "p_user_id" "uuid", "p_name" "text", "p_description" "text", "p_category_id" integer, "p_variable_overrides" "jsonb", "p_file_paths" "text"[]) RETURNS "json"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    v_file_path text;
    v_file_id integer;
    v_rows_affected integer;
BEGIN
    -- Update the dataset
    UPDATE public.datasets
    SET name = p_name,
        description = p_description,
        category_id = p_category_id,
        variable_overrides = p_variable_overrides
    WHERE id = p_dataset_id AND user_id = p_user_id;

    GET DIAGNOSTICS v_rows_affected = ROW_COUNT;

    -- Check if the dataset was actually updated
    IF v_rows_affected = 0 THEN
        RAISE EXCEPTION 'Dataset not found or user does not have permission to update it';
    END IF;

    RAISE NOTICE 'Dataset updated successfully. Rows affected: %', v_rows_affected;

    -- Remove existing file associations
    DELETE FROM public.dataset_files WHERE dataset_id = p_dataset_id;
    
    RAISE NOTICE 'Existing file associations removed for dataset ID: %', p_dataset_id;

    -- Add new file associations
    FOREACH v_file_path IN ARRAY p_file_paths LOOP
        -- Get the file ID from the file table
        SELECT id INTO v_file_id
        FROM public.files
        WHERE file_path = v_file_path AND user_id = p_user_id;

        IF v_file_id IS NOT NULL THEN
            INSERT INTO public.dataset_files (dataset_id, file_id)
            VALUES (p_dataset_id, v_file_id);
            RAISE NOTICE 'File associated with dataset. Dataset ID: %, File ID: %', p_dataset_id, v_file_id;
        ELSE
            RAISE WARNING 'File not found or user does not have permission: %', v_file_path;
        END IF;
    END LOOP;

    RETURN json_build_object('dataset_id', p_dataset_id);
EXCEPTION
    WHEN OTHERS THEN
        -- Log the error and re-raise
        RAISE NOTICE 'Error in update_dataset_with_files: %', SQLERRM;
        RAISE;
END;
$$;


ALTER FUNCTION "public"."update_dataset_with_files"("p_dataset_id" integer, "p_user_id" "uuid", "p_name" "text", "p_description" "text", "p_category_id" integer, "p_variable_overrides" "jsonb", "p_file_paths" "text"[]) OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_dataset_with_files"("p_dataset_id" integer, "p_user_id" "uuid", "p_name" "text", "p_description" "text", "p_category_id" integer, "p_variable_overrides" "jsonb", "p_file_paths" "text"[], "p_file_types" "text"[]) RETURNS "json"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    v_file_path text;
    v_file_id integer;
    v_file_type text;
    v_rows_affected integer;
    v_dataset_exists boolean;
BEGIN
    -- Check if the dataset exists and belongs to the user
    SELECT EXISTS (
        SELECT 1 
        FROM public.datasets 
        WHERE id = p_dataset_id AND user_id = p_user_id
    ) INTO v_dataset_exists;

    IF NOT v_dataset_exists THEN
        RAISE EXCEPTION 'Dataset not found or user does not have permission to update it';
    END IF;

    -- Update the dataset
    UPDATE public.datasets
    SET name = p_name,
        description = p_description,
        category_id = p_category_id,
        variable_overrides = p_variable_overrides
    WHERE id = p_dataset_id AND user_id = p_user_id;

    GET DIAGNOSTICS v_rows_affected = ROW_COUNT;

    -- Remove existing file associations
    DELETE FROM public.dataset_files WHERE dataset_id = p_dataset_id;

    -- Add new file associations
    FOR i IN 1..array_length(p_file_paths, 1) LOOP
        v_file_path := p_file_paths[i];
        v_file_type := p_file_types[i];

        -- Check if the file exists and belongs to the user
        SELECT id INTO v_file_id
        FROM public.files
        WHERE file_path = v_file_path AND user_id = p_user_id;

        IF v_file_id IS NOT NULL THEN
            INSERT INTO public.dataset_files (dataset_id, file_id, file_type)
            VALUES (p_dataset_id, v_file_id, v_file_type);
        END IF;
    END LOOP;

    RETURN json_build_object('dataset_id', p_dataset_id);
END;
$$;


ALTER FUNCTION "public"."update_dataset_with_files"("p_dataset_id" integer, "p_user_id" "uuid", "p_name" "text", "p_description" "text", "p_category_id" integer, "p_variable_overrides" "jsonb", "p_file_paths" "text"[], "p_file_types" "text"[]) OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_descendant_paths"("category_id" integer, "old_path" "public"."ltree", "new_path" "public"."ltree") RETURNS "void"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    -- Update all descendant paths
    UPDATE categories 
    SET path = new_path || subpath(path, nlevel(old_path))
    WHERE path <@ old_path AND id != category_id;
END;
$$;


ALTER FUNCTION "public"."update_descendant_paths"("category_id" integer, "old_path" "public"."ltree", "new_path" "public"."ltree") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_gui_components_updated_at"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    NEW.updated_at = TIMEZONE('utc'::text, NOW());
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_gui_components_updated_at"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_job_status_with_lock"("p_parent_job_id" integer, "p_child_task_status" "text", "p_parent_job_table_name" "text") RETURNS "text"
    LANGUAGE "plpgsql"
    AS $_$
DECLARE
    v_current_status TEXT;
    v_completed INT;
    v_failed INT;
    v_total INT;
    v_new_status TEXT;
BEGIN
    -- Lock the row in the parent 'jobs' table (formerly 'batches')
    EXECUTE format('SELECT status FROM public.%I WHERE id = $1 FOR UPDATE', p_parent_job_table_name)
    USING p_parent_job_id
    INTO v_current_status;

    -- Get total number of child 'tasks' (formerly 'jobs') for this parent 'job' (formerly 'batch')
    -- Querying new 'tasks' table on new 'job_id' column, which links back to the parent 'jobs' table ID
    EXECUTE format('SELECT COUNT(*) FROM public.tasks WHERE job_id = $1')
    USING p_parent_job_id
    INTO v_total;

    -- Parse current status of the parent 'job'
    IF v_current_status = 'queued' OR v_current_status IS NULL THEN -- Handle initial state
        v_completed := 0;
        v_failed := 0;
    ELSE
        -- Assuming status format "completed/failed" or "completed/failed/total"
        -- The total is now dynamically calculated, so we only need completed and failed from stored status.
        v_completed := COALESCE(split_part(v_current_status, '/', 1)::INT, 0);
        v_failed := COALESCE(split_part(v_current_status, '/', 2)::INT, 0);
    END IF;

    -- Update counters based on child 'task' status
    IF p_child_task_status = 'complete' THEN
        v_completed := v_completed + 1;
    ELSIF p_child_task_status = 'error' THEN
        v_failed := v_failed + 1;
    END IF;

    -- Construct new status string for the parent 'job'
    v_new_status := v_completed || '/' || v_failed || '/' || v_total;

    -- Update status in the parent 'jobs' table
    EXECUTE format('UPDATE public.%I SET status = $1 WHERE id = $2', p_parent_job_table_name)
    USING v_new_status, p_parent_job_id;

    RETURN v_new_status;
END;
$_$;


ALTER FUNCTION "public"."update_job_status_with_lock"("p_parent_job_id" integer, "p_child_task_status" "text", "p_parent_job_table_name" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_log_fields_updated_at"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_log_fields_updated_at"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_template_data"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    -- Only process commented_json if it's not null
    IF NEW.commented_json IS NOT NULL THEN
        NEW.template_data := public.remove_json_comments(NEW.commented_json);
    END IF;
    
    -- Only process commented_vars if it's not null
    IF NEW.commented_vars IS NOT NULL THEN
        NEW.vars := public.remove_json_comments(NEW.commented_vars);
    END IF;
    
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_template_data"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_template_data_optimized"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    -- Only process commented_json if it's not null
    IF NEW.commented_json IS NOT NULL THEN
        NEW.template_data := public.remove_json_comments_optimized(NEW.commented_json);
    END IF;
    
    -- Only process commented_vars if it's not null
    IF NEW.commented_vars IS NOT NULL THEN
        NEW.vars := public.remove_json_comments_optimized(NEW.commented_vars);
    END IF;
    
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_template_data_optimized"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_tickets_updated_at"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    NEW.updated_at = timezone('utc'::text, now());
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_tickets_updated_at"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_updated_at_column"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_updated_at_column"() OWNER TO "postgres";


COMMENT ON FUNCTION "public"."update_updated_at_column"() IS 'Automatically updates updated_at timestamp on row modifications';



CREATE OR REPLACE FUNCTION "public"."update_user_notification_states_updated_at"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    NEW.updated_at = timezone('utc'::text, now());
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_user_notification_states_updated_at"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."validate_file_types_array"("file_types" "jsonb") RETURNS boolean
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    -- Check if it's an array
    IF jsonb_typeof(file_types) != 'array' THEN
        RETURN FALSE;
    END IF;
    
    -- Check if all elements are strings
    RETURN NOT EXISTS (
        SELECT 1 
        FROM jsonb_array_elements(file_types) AS elem
        WHERE jsonb_typeof(elem) != 'string'
    );
END;
$$;


ALTER FUNCTION "public"."validate_file_types_array"("file_types" "jsonb") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."verify_file_ownership"("p_file_id" integer, "p_object_type" "text", "p_object_id" integer) RETURNS "void"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    v_file_owner uuid;
    v_object_owner uuid;
BEGIN
    SET search_path TO public;
    -- Get the owner of the file
    SELECT user_id INTO v_file_owner
    FROM files
    WHERE id = p_file_id;

    -- Get the owner of the object (dataset, category, or job_template)
    CASE p_object_type
        WHEN 'dataset' THEN
            SELECT user_id INTO v_object_owner
            FROM datasets
            WHERE id = p_object_id;
        WHEN 'category' THEN
            SELECT user_id INTO v_object_owner
            FROM categories
            WHERE id = p_object_id;
        WHEN 'job_template' THEN
            SELECT user_id INTO v_object_owner
            FROM global_job_templates
            WHERE id = p_object_id;
    END CASE;
    -- Compare the owners
    
    IF v_file_owner != v_object_owner THEN
        RAISE EXCEPTION 'File (ID: %) does not belong to the owner of the % (ID: %)', 
                        p_file_id, p_object_type, p_object_id;
    END IF;
    
END;
$$;


ALTER FUNCTION "public"."verify_file_ownership"("p_file_id" integer, "p_object_type" "text", "p_object_id" integer) OWNER TO "postgres";

SET default_tablespace = '';

SET default_table_access_method = "heap";


CREATE TABLE IF NOT EXISTS "public"."bulk_task_tasks" (
    "bulk_task_id" integer NOT NULL,
    "task_id" integer NOT NULL,
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."bulk_task_tasks" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."categories" (
    "id" integer NOT NULL,
    "user_id" "uuid" NOT NULL,
    "name" character varying(255) NOT NULL,
    "description" "text",
    "parent_category_id" integer,
    "variable_overrides" "jsonb",
    "path" "public"."ltree" NOT NULL
);


ALTER TABLE "public"."categories" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."categories_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."categories_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."categories_id_seq" OWNED BY "public"."categories"."id";



CREATE TABLE IF NOT EXISTS "public"."category_files" (
    "category_id" integer NOT NULL,
    "file_id" integer NOT NULL,
    "file_type" "text",
    "id" bigint NOT NULL,
    "user_id" "uuid" NOT NULL
);


ALTER TABLE "public"."category_files" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."category_files_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."category_files_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."category_files_id_seq" OWNED BY "public"."category_files"."id";



CREATE TABLE IF NOT EXISTS "public"."category_job_templates" (
    "category_id" integer NOT NULL,
    "global_job_template_id" integer NOT NULL
);


ALTER TABLE "public"."category_job_templates" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."category_levels" (
    "id" bigint NOT NULL,
    "user_id" "uuid" NOT NULL,
    "level_index" integer NOT NULL,
    "label" "text" NOT NULL,
    "description" "text",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."category_levels" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."category_levels_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."category_levels_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."category_levels_id_seq" OWNED BY "public"."category_levels"."id";



CREATE TABLE IF NOT EXISTS "public"."dataset_files" (
    "dataset_id" integer NOT NULL,
    "file_id" integer NOT NULL,
    "file_type" "text"
);


ALTER TABLE "public"."dataset_files" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."datasets" (
    "id" integer NOT NULL,
    "user_id" "uuid" NOT NULL,
    "category_id" integer,
    "name" character varying(255) NOT NULL,
    "description" "text",
    "variable_overrides" "jsonb",
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "path" "public"."ltree"
);


ALTER TABLE "public"."datasets" OWNER TO "postgres";


COMMENT ON COLUMN "public"."datasets"."created_at" IS 'Timestamp when the dataset was created';



COMMENT ON COLUMN "public"."datasets"."updated_at" IS 'Timestamp when the dataset was last updated';



CREATE SEQUENCE IF NOT EXISTS "public"."datasets_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."datasets_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."datasets_id_seq" OWNED BY "public"."datasets"."id";



CREATE TABLE IF NOT EXISTS "public"."files" (
    "id" integer NOT NULL,
    "user_id" "uuid" NOT NULL,
    "dataset_id" integer,
    "bucket_name" character varying(255) NOT NULL,
    "file_path" character varying(255) NOT NULL,
    "file_name" character varying(255) NOT NULL,
    "file_size" integer,
    "content_type" character varying(255),
    "created_at" timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE "public"."files" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."files_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."files_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."files_id_seq" OWNED BY "public"."files"."id";



CREATE TABLE IF NOT EXISTS "public"."global_job_templates" (
    "id" integer NOT NULL,
    "user_id" "uuid" NOT NULL,
    "name" character varying(255) NOT NULL,
    "description" "text",
    "template_data" "jsonb",
    "vars" "jsonb",
    "commented_json" "text",
    "commented_vars" "text"
);


ALTER TABLE "public"."global_job_templates" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."global_job_templates_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."global_job_templates_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."global_job_templates_id_seq" OWNED BY "public"."global_job_templates"."id";



CREATE TABLE IF NOT EXISTS "public"."gui_components" (
    "id" "text" NOT NULL,
    "description" "text",
    "parameters" "jsonb" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    "component_name" "text" NOT NULL,
    CONSTRAINT "gui_components_component_name_not_empty" CHECK (("component_name" <> ''::"text")),
    CONSTRAINT "gui_components_parameters_check" CHECK (("parameters" IS NOT NULL))
);


ALTER TABLE "public"."gui_components" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."job_template_files" (
    "job_template_id" integer NOT NULL,
    "file_id" integer NOT NULL,
    "file_type" "text"
);


ALTER TABLE "public"."job_template_files" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."jobs" (
    "id" integer NOT NULL,
    "user_id" "uuid" NOT NULL,
    "name" character varying(255) NOT NULL,
    "description" "text",
    "status" character varying(50) NOT NULL,
    "created_at" timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updated_at" timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE "public"."jobs" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."jobs_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."jobs_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."jobs_id_seq" OWNED BY "public"."jobs"."id";



CREATE TABLE IF NOT EXISTS "public"."log_fields" (
    "id" integer NOT NULL,
    "group_name" "text" NOT NULL,
    "field_data" "jsonb" NOT NULL,
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."log_fields" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."log_fields_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."log_fields_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."log_fields_id_seq" OWNED BY "public"."log_fields"."id";



CREATE TABLE IF NOT EXISTS "public"."profiles" (
    "id" bigint NOT NULL,
    "user_id" "uuid" NOT NULL,
    "name" "text" NOT NULL,
    "description" "text",
    "required_file_types" "jsonb" NOT NULL,
    "optional_file_types" "jsonb" DEFAULT '[]'::"jsonb" NOT NULL,
    "default_variables" "jsonb" DEFAULT '{}'::"jsonb" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    CONSTRAINT "check_default_variables_object" CHECK (("jsonb_typeof"("default_variables") = 'object'::"text")),
    CONSTRAINT "check_optional_file_types_array" CHECK ("public"."validate_file_types_array"("optional_file_types")),
    CONSTRAINT "check_required_file_types_array" CHECK ("public"."validate_file_types_array"("required_file_types"))
);


ALTER TABLE "public"."profiles" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."profiles_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."profiles_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."profiles_id_seq" OWNED BY "public"."profiles"."id";



CREATE TABLE IF NOT EXISTS "public"."task_results" (
    "id" integer NOT NULL,
    "task_id" integer NOT NULL,
    "file_name" "text" NOT NULL,
    "file_path" "text" NOT NULL,
    "file_size" integer,
    "content_type" "text",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "file_type" "text",
    "visible" boolean DEFAULT true,
    "required" boolean DEFAULT true
);


ALTER TABLE "public"."task_results" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."task_results_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."task_results_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."task_results_id_seq" OWNED BY "public"."task_results"."id";



CREATE TABLE IF NOT EXISTS "public"."tasks" (
    "id" integer NOT NULL,
    "user_id" "uuid" NOT NULL,
    "name" character varying(255) NOT NULL,
    "description" "text",
    "global_job_template_id" integer NOT NULL,
    "dataset_id" integer,
    "job_json" "jsonb",
    "status" character varying(50) NOT NULL,
    "created_at" timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updated_at" timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "job_id" integer,
    "workervars" "jsonb",
    "vars" "jsonb",
    "result" "jsonb",
    "bulk_job_type" "public"."bulk_job_type"
);


ALTER TABLE "public"."tasks" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."tasks_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."tasks_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."tasks_id_seq" OWNED BY "public"."tasks"."id";



CREATE TABLE IF NOT EXISTS "public"."ticket_messages" (
    "id" integer NOT NULL,
    "ticket_id" integer NOT NULL,
    "author_id" "uuid" NOT NULL,
    "body" "text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL
);


ALTER TABLE "public"."ticket_messages" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."ticket_messages_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."ticket_messages_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."ticket_messages_id_seq" OWNED BY "public"."ticket_messages"."id";



CREATE TABLE IF NOT EXISTS "public"."ticket_status_history" (
    "id" integer NOT NULL,
    "ticket_id" integer NOT NULL,
    "old_status" "public"."ticket_status",
    "new_status" "public"."ticket_status" NOT NULL,
    "changed_by" "uuid" NOT NULL,
    "changed_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL
);


ALTER TABLE "public"."ticket_status_history" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."ticket_status_history_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."ticket_status_history_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."ticket_status_history_id_seq" OWNED BY "public"."ticket_status_history"."id";



CREATE TABLE IF NOT EXISTS "public"."ticket_targets" (
    "id" integer NOT NULL,
    "ticket_id" integer NOT NULL,
    "target_type" "text" NOT NULL,
    "target_id" integer NOT NULL,
    CONSTRAINT "ticket_targets_target_type_check" CHECK (("target_type" = ANY (ARRAY['job'::"text", 'task'::"text", 'dataset'::"text"])))
);


ALTER TABLE "public"."ticket_targets" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."ticket_targets_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."ticket_targets_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."ticket_targets_id_seq" OWNED BY "public"."ticket_targets"."id";



CREATE TABLE IF NOT EXISTS "public"."tickets" (
    "id" integer NOT NULL,
    "creator_id" "uuid" NOT NULL,
    "assignee_id" "uuid",
    "title" "text" NOT NULL,
    "description" "text",
    "status" "public"."ticket_status" DEFAULT 'open'::"public"."ticket_status" NOT NULL,
    "priority" "public"."ticket_priority" DEFAULT 'medium'::"public"."ticket_priority" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    "updated_by" "uuid"
);


ALTER TABLE "public"."tickets" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."tickets_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."tickets_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."tickets_id_seq" OWNED BY "public"."tickets"."id";



CREATE TABLE IF NOT EXISTS "public"."user_notification_states" (
    "user_id" "uuid" NOT NULL,
    "last_seen_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    "created_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL
);


ALTER TABLE "public"."user_notification_states" OWNER TO "postgres";


ALTER TABLE ONLY "public"."categories" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."categories_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."category_files" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."category_files_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."category_levels" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."category_levels_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."datasets" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."datasets_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."files" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."files_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."global_job_templates" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."global_job_templates_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."jobs" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."jobs_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."log_fields" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."log_fields_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."profiles" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."profiles_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."task_results" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."task_results_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."tasks" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."tasks_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."ticket_messages" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."ticket_messages_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."ticket_status_history" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."ticket_status_history_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."ticket_targets" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."ticket_targets_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."tickets" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."tickets_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."bulk_task_tasks"
    ADD CONSTRAINT "bulk_task_tasks_pkey" PRIMARY KEY ("bulk_task_id", "task_id");



ALTER TABLE ONLY "public"."categories"
    ADD CONSTRAINT "categories_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."category_files"
    ADD CONSTRAINT "category_files_pkey" PRIMARY KEY ("category_id", "file_id");



ALTER TABLE ONLY "public"."category_job_templates"
    ADD CONSTRAINT "category_job_templates_pkey" PRIMARY KEY ("category_id", "global_job_template_id");



ALTER TABLE ONLY "public"."category_levels"
    ADD CONSTRAINT "category_levels_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."dataset_files"
    ADD CONSTRAINT "dataset_files_pkey" PRIMARY KEY ("dataset_id", "file_id");



ALTER TABLE ONLY "public"."datasets"
    ADD CONSTRAINT "datasets_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."files"
    ADD CONSTRAINT "files_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."global_job_templates"
    ADD CONSTRAINT "global_job_templates_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."gui_components"
    ADD CONSTRAINT "gui_components_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."job_template_files"
    ADD CONSTRAINT "job_template_files_pkey" PRIMARY KEY ("job_template_id", "file_id");



ALTER TABLE ONLY "public"."jobs"
    ADD CONSTRAINT "jobs_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."log_fields"
    ADD CONSTRAINT "log_fields_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."profiles"
    ADD CONSTRAINT "profiles_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."task_results"
    ADD CONSTRAINT "task_results_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."tasks"
    ADD CONSTRAINT "tasks_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."ticket_messages"
    ADD CONSTRAINT "ticket_messages_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."ticket_status_history"
    ADD CONSTRAINT "ticket_status_history_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."ticket_targets"
    ADD CONSTRAINT "ticket_targets_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."tickets"
    ADD CONSTRAINT "tickets_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."profiles"
    ADD CONSTRAINT "uq_profile_name" UNIQUE ("user_id", "name");



ALTER TABLE ONLY "public"."category_levels"
    ADD CONSTRAINT "uq_user_level" UNIQUE ("user_id", "level_index");



ALTER TABLE ONLY "public"."user_notification_states"
    ADD CONSTRAINT "user_notification_states_pkey" PRIMARY KEY ("user_id");



CREATE INDEX "idx_bulk_task_tasks_task_id" ON "public"."bulk_task_tasks" USING "btree" ("task_id");



CREATE INDEX "idx_categories_path_btree" ON "public"."categories" USING "btree" ("path");



CREATE INDEX "idx_categories_path_gist" ON "public"."categories" USING "gist" ("path");



CREATE INDEX "idx_categories_variable_overrides_gin" ON "public"."categories" USING "gin" ("variable_overrides");



CREATE INDEX "idx_dataset_files_dataset_id" ON "public"."dataset_files" USING "btree" ("dataset_id");



CREATE INDEX "idx_datasets_user_id" ON "public"."datasets" USING "btree" ("user_id");



CREATE INDEX "idx_datasets_variable_overrides_gin" ON "public"."datasets" USING "gin" ("variable_overrides");



CREATE INDEX "idx_global_job_templates_user_id" ON "public"."global_job_templates" USING "btree" ("user_id");



CREATE INDEX "idx_gui_components_id" ON "public"."gui_components" USING "btree" ("id");



CREATE INDEX "idx_jobs_status" ON "public"."jobs" USING "btree" ("status");



CREATE INDEX "idx_jobs_user_id" ON "public"."jobs" USING "btree" ("user_id");



CREATE INDEX "idx_jobs_user_status_created" ON "public"."jobs" USING "btree" ("user_id", "status", "created_at" DESC);



CREATE INDEX "idx_log_fields_group_name" ON "public"."log_fields" USING "btree" ("group_name");



CREATE INDEX "idx_task_results_task_id" ON "public"."task_results" USING "btree" ("task_id");



CREATE INDEX "idx_tasks_job_id" ON "public"."tasks" USING "btree" ("job_id");



CREATE INDEX "idx_tasks_status_created_at" ON "public"."tasks" USING "btree" ("status", "created_at") WHERE (("status")::"text" = 'queued'::"text");



COMMENT ON INDEX "public"."idx_tasks_status_created_at" IS 'Index to optimize task queue processing with SKIP LOCKED';



CREATE INDEX "idx_tasks_user_id" ON "public"."tasks" USING "btree" ("user_id");



CREATE INDEX "idx_tasks_user_job_status" ON "public"."tasks" USING "btree" ("user_id", "job_id", "status");



CREATE INDEX "idx_ticket_messages_ticket_id" ON "public"."ticket_messages" USING "btree" ("ticket_id");



CREATE INDEX "idx_ticket_status_history_ticket_id" ON "public"."ticket_status_history" USING "btree" ("ticket_id");



CREATE INDEX "idx_ticket_targets_target" ON "public"."ticket_targets" USING "btree" ("target_type", "target_id");



CREATE INDEX "idx_ticket_targets_ticket_id" ON "public"."ticket_targets" USING "btree" ("ticket_id");



CREATE INDEX "idx_tickets_assignee_id" ON "public"."tickets" USING "btree" ("assignee_id");



CREATE INDEX "idx_tickets_created_at" ON "public"."tickets" USING "btree" ("created_at");



CREATE INDEX "idx_tickets_creator_id" ON "public"."tickets" USING "btree" ("creator_id");



CREATE INDEX "idx_tickets_status" ON "public"."tickets" USING "btree" ("status");



CREATE INDEX "idx_user_notification_states_last_seen_at" ON "public"."user_notification_states" USING "btree" ("last_seen_at");



CREATE INDEX "idx_user_notification_states_user_id" ON "public"."user_notification_states" USING "btree" ("user_id");



CREATE INDEX "ix_cl_label" ON "public"."category_levels" USING "btree" ("label");



CREATE INDEX "ix_cl_level_index" ON "public"."category_levels" USING "btree" ("level_index");



CREATE INDEX "ix_cl_user" ON "public"."category_levels" USING "btree" ("user_id");



CREATE INDEX "ix_prof_opt_gin" ON "public"."profiles" USING "gin" ("optional_file_types" "jsonb_path_ops");



CREATE INDEX "ix_prof_req_gin" ON "public"."profiles" USING "gin" ("required_file_types" "jsonb_path_ops");



CREATE INDEX "ix_prof_vars_gin" ON "public"."profiles" USING "gin" ("default_variables");



CREATE INDEX "ix_profiles_name" ON "public"."profiles" USING "btree" ("name");



CREATE INDEX "ix_profiles_user_id" ON "public"."profiles" USING "btree" ("user_id");



CREATE OR REPLACE TRIGGER "set_path" BEFORE INSERT OR UPDATE ON "public"."categories" FOR EACH ROW EXECUTE FUNCTION "public"."trg_set_path"();



CREATE OR REPLACE TRIGGER "sync_category_files_trigger" AFTER INSERT OR UPDATE ON "public"."categories" FOR EACH ROW EXECUTE FUNCTION "public"."sync_category_files"();



CREATE OR REPLACE TRIGGER "sync_dataset_path" BEFORE INSERT OR UPDATE ON "public"."datasets" FOR EACH ROW EXECUTE FUNCTION "public"."trg_sync_dataset_path"();



CREATE OR REPLACE TRIGGER "sync_job_template_files_trigger" AFTER INSERT OR UPDATE ON "public"."global_job_templates" FOR EACH ROW EXECUTE FUNCTION "public"."sync_job_template_files"();



CREATE OR REPLACE TRIGGER "ticket_status_change_trigger" AFTER UPDATE ON "public"."tickets" FOR EACH ROW EXECUTE FUNCTION "public"."track_ticket_status_changes"();



CREATE OR REPLACE TRIGGER "trigger_gui_components_updated_at" BEFORE UPDATE ON "public"."gui_components" FOR EACH ROW EXECUTE FUNCTION "public"."update_gui_components_updated_at"();



CREATE OR REPLACE TRIGGER "trigger_log_fields_updated_at" BEFORE UPDATE ON "public"."log_fields" FOR EACH ROW EXECUTE FUNCTION "public"."update_log_fields_updated_at"();



CREATE OR REPLACE TRIGGER "trigger_update_user_notification_states_updated_at" BEFORE UPDATE ON "public"."user_notification_states" FOR EACH ROW EXECUTE FUNCTION "public"."update_user_notification_states_updated_at"();



CREATE OR REPLACE TRIGGER "update_category_levels_updated_at" BEFORE UPDATE ON "public"."category_levels" FOR EACH ROW EXECUTE FUNCTION "public"."update_category_levels_updated_at"();



CREATE OR REPLACE TRIGGER "update_dataset_paths_on_category_change" AFTER INSERT OR UPDATE ON "public"."categories" FOR EACH ROW EXECUTE FUNCTION "public"."trg_update_dataset_paths_on_category_change"();



CREATE OR REPLACE TRIGGER "update_datasets_updated_at" BEFORE UPDATE ON "public"."datasets" FOR EACH ROW EXECUTE FUNCTION "public"."update_updated_at_column"();



CREATE OR REPLACE TRIGGER "update_descendant_paths_trigger" AFTER UPDATE ON "public"."categories" FOR EACH ROW EXECUTE FUNCTION "public"."trg_update_descendant_paths"();



CREATE OR REPLACE TRIGGER "update_jobs_updated_at" BEFORE UPDATE ON "public"."jobs" FOR EACH ROW EXECUTE FUNCTION "public"."update_updated_at_column"();



COMMENT ON TRIGGER "update_jobs_updated_at" ON "public"."jobs" IS 'Ensures updated_at is set on job status changes';



CREATE OR REPLACE TRIGGER "update_tasks_updated_at" BEFORE UPDATE ON "public"."tasks" FOR EACH ROW EXECUTE FUNCTION "public"."update_updated_at_column"();



COMMENT ON TRIGGER "update_tasks_updated_at" ON "public"."tasks" IS 'Ensures updated_at is set on task status changes';



CREATE OR REPLACE TRIGGER "update_template_data_trigger" BEFORE INSERT OR UPDATE OF "commented_json", "commented_vars" ON "public"."global_job_templates" FOR EACH ROW EXECUTE FUNCTION "public"."update_template_data"();



CREATE OR REPLACE TRIGGER "update_tickets_updated_at_trigger" BEFORE UPDATE ON "public"."tickets" FOR EACH ROW EXECUTE FUNCTION "public"."update_tickets_updated_at"();



ALTER TABLE ONLY "public"."jobs"
    ADD CONSTRAINT "batches_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."bulk_task_tasks"
    ADD CONSTRAINT "bulk_task_tasks_bulk_task_id_fkey" FOREIGN KEY ("bulk_task_id") REFERENCES "public"."tasks"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."bulk_task_tasks"
    ADD CONSTRAINT "bulk_task_tasks_task_id_fkey" FOREIGN KEY ("task_id") REFERENCES "public"."tasks"("id");



ALTER TABLE ONLY "public"."categories"
    ADD CONSTRAINT "categories_parent_category_id_fkey" FOREIGN KEY ("parent_category_id") REFERENCES "public"."categories"("id");



ALTER TABLE ONLY "public"."categories"
    ADD CONSTRAINT "categories_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."category_files"
    ADD CONSTRAINT "category_files_category_id_fkey" FOREIGN KEY ("category_id") REFERENCES "public"."categories"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."category_files"
    ADD CONSTRAINT "category_files_file_id_fkey" FOREIGN KEY ("file_id") REFERENCES "public"."files"("id");



ALTER TABLE ONLY "public"."category_files"
    ADD CONSTRAINT "category_files_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."category_job_templates"
    ADD CONSTRAINT "category_job_templates_category_id_fkey" FOREIGN KEY ("category_id") REFERENCES "public"."categories"("id");



ALTER TABLE ONLY "public"."category_job_templates"
    ADD CONSTRAINT "category_job_templates_global_job_template_id_fkey" FOREIGN KEY ("global_job_template_id") REFERENCES "public"."global_job_templates"("id");



ALTER TABLE ONLY "public"."category_levels"
    ADD CONSTRAINT "category_levels_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."dataset_files"
    ADD CONSTRAINT "dataset_files_dataset_id_fkey" FOREIGN KEY ("dataset_id") REFERENCES "public"."datasets"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."dataset_files"
    ADD CONSTRAINT "dataset_files_file_id_fkey" FOREIGN KEY ("file_id") REFERENCES "public"."files"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."datasets"
    ADD CONSTRAINT "datasets_category_id_fkey" FOREIGN KEY ("category_id") REFERENCES "public"."categories"("id");



ALTER TABLE ONLY "public"."datasets"
    ADD CONSTRAINT "datasets_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."files"
    ADD CONSTRAINT "files_dataset_id_fkey" FOREIGN KEY ("dataset_id") REFERENCES "public"."datasets"("id");



ALTER TABLE ONLY "public"."files"
    ADD CONSTRAINT "files_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."global_job_templates"
    ADD CONSTRAINT "global_job_templates_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."job_template_files"
    ADD CONSTRAINT "job_template_files_file_id_fkey" FOREIGN KEY ("file_id") REFERENCES "public"."files"("id");



ALTER TABLE ONLY "public"."job_template_files"
    ADD CONSTRAINT "job_template_files_job_template_id_fkey" FOREIGN KEY ("job_template_id") REFERENCES "public"."global_job_templates"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."tasks"
    ADD CONSTRAINT "jobs_dataset_id_fkey" FOREIGN KEY ("dataset_id") REFERENCES "public"."datasets"("id");



ALTER TABLE ONLY "public"."tasks"
    ADD CONSTRAINT "jobs_global_job_template_id_fkey" FOREIGN KEY ("global_job_template_id") REFERENCES "public"."global_job_templates"("id");



ALTER TABLE ONLY "public"."tasks"
    ADD CONSTRAINT "jobs_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."profiles"
    ADD CONSTRAINT "profiles_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."task_results"
    ADD CONSTRAINT "task_results_task_id_fkey" FOREIGN KEY ("task_id") REFERENCES "public"."tasks"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."tasks"
    ADD CONSTRAINT "tasks_job_id_fkey" FOREIGN KEY ("job_id") REFERENCES "public"."jobs"("id");



ALTER TABLE ONLY "public"."ticket_messages"
    ADD CONSTRAINT "ticket_messages_author_id_fkey" FOREIGN KEY ("author_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."ticket_messages"
    ADD CONSTRAINT "ticket_messages_ticket_id_fkey" FOREIGN KEY ("ticket_id") REFERENCES "public"."tickets"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."ticket_status_history"
    ADD CONSTRAINT "ticket_status_history_changed_by_fkey" FOREIGN KEY ("changed_by") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."ticket_status_history"
    ADD CONSTRAINT "ticket_status_history_ticket_id_fkey" FOREIGN KEY ("ticket_id") REFERENCES "public"."tickets"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."ticket_targets"
    ADD CONSTRAINT "ticket_targets_ticket_id_fkey" FOREIGN KEY ("ticket_id") REFERENCES "public"."tickets"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."tickets"
    ADD CONSTRAINT "tickets_assignee_id_fkey" FOREIGN KEY ("assignee_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."tickets"
    ADD CONSTRAINT "tickets_creator_id_fkey" FOREIGN KEY ("creator_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."tickets"
    ADD CONSTRAINT "tickets_updated_by_fkey" FOREIGN KEY ("updated_by") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."user_notification_states"
    ADD CONSTRAINT "user_notification_states_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



CREATE POLICY "Allow admins to modify log_fields" ON "public"."log_fields" TO "authenticated" USING ((("auth"."jwt"() ? 'role'::"text") AND (("auth"."jwt"() ->> 'role'::"text") = 'admin'::"text")));



CREATE POLICY "Allow all users to read log_fields" ON "public"."log_fields" FOR SELECT TO "authenticated" USING (true);



CREATE POLICY "Allow full access to service role" ON "public"."gui_components" TO "service_role" USING (true) WITH CHECK (true);



CREATE POLICY "Allow read access for all authenticated users" ON "public"."gui_components" FOR SELECT TO "authenticated" USING (true);



CREATE POLICY "Users can delete their own categories" ON "public"."categories" FOR DELETE USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can insert own notification state" ON "public"."user_notification_states" FOR INSERT WITH CHECK (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can insert their own categories" ON "public"."categories" FOR INSERT WITH CHECK (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can manage their own category levels" ON "public"."category_levels" USING (("user_id" = "auth"."uid"())) WITH CHECK (("user_id" = "auth"."uid"()));



CREATE POLICY "Users can manage their own profiles" ON "public"."profiles" USING (("user_id" = "auth"."uid"())) WITH CHECK (("user_id" = "auth"."uid"()));



CREATE POLICY "Users can update own notification state" ON "public"."user_notification_states" FOR UPDATE USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can update their own categories" ON "public"."categories" FOR UPDATE USING (("auth"."uid"() = "user_id")) WITH CHECK (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can view own notification state" ON "public"."user_notification_states" FOR SELECT USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can view their own categories" ON "public"."categories" FOR SELECT USING (("auth"."uid"() = "user_id"));



CREATE POLICY "batches_policy" ON "public"."jobs" USING (("auth"."uid"() = "user_id"));



ALTER TABLE "public"."bulk_task_tasks" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "bulk_task_tasks_policy" ON "public"."bulk_task_tasks" USING ((EXISTS ( SELECT 1
   FROM "public"."tasks"
  WHERE (("tasks"."id" = "bulk_task_tasks"."bulk_task_id") AND ("tasks"."user_id" = "auth"."uid"())))));



ALTER TABLE "public"."categories" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "categories_policy" ON "public"."categories" USING (("auth"."uid"() = "user_id"));



ALTER TABLE "public"."category_files" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "category_files_access_policy" ON "public"."category_files" USING ((EXISTS ( SELECT 1
   FROM "public"."categories"
  WHERE (("categories"."id" = "category_files"."category_id") AND ("categories"."user_id" = "auth"."uid"())))));



ALTER TABLE "public"."category_job_templates" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "category_job_templates_policy" ON "public"."category_job_templates" USING ((EXISTS ( SELECT 1
   FROM "public"."categories"
  WHERE (("categories"."id" = "category_job_templates"."category_id") AND ("categories"."user_id" = "auth"."uid"())))));



ALTER TABLE "public"."category_levels" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."dataset_files" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "dataset_files_access_policy" ON "public"."dataset_files" USING ((EXISTS ( SELECT 1
   FROM ("public"."datasets" "d"
     JOIN "public"."files" "f" ON (("f"."id" = "dataset_files"."file_id")))
  WHERE (("d"."id" = "dataset_files"."dataset_id") AND ("d"."user_id" = "auth"."uid"()) AND ("f"."user_id" = "auth"."uid"())))));



ALTER TABLE "public"."datasets" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "datasets" ON "public"."datasets" TO "authenticated" USING (("auth"."uid"() = "user_id"));



ALTER TABLE "public"."files" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "files_policy2" ON "public"."files" USING (("auth"."uid"() = "user_id"));



ALTER TABLE "public"."global_job_templates" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "global_job_templates_policy" ON "public"."global_job_templates" USING (("auth"."uid"() = "user_id"));



ALTER TABLE "public"."gui_components" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."job_template_files" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "job_template_files_access_policy" ON "public"."job_template_files" USING ((EXISTS ( SELECT 1
   FROM "public"."global_job_templates"
  WHERE (("global_job_templates"."id" = "job_template_files"."job_template_id") AND ("global_job_templates"."user_id" = "auth"."uid"())))));



ALTER TABLE "public"."jobs" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "jobs_policy" ON "public"."tasks" USING (("auth"."uid"() = "user_id"));



ALTER TABLE "public"."log_fields" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."profiles" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."task_results" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "task_results_policy" ON "public"."task_results" USING ((EXISTS ( SELECT 1
   FROM "public"."tasks"
  WHERE (("tasks"."id" = "task_results"."task_id") AND ("tasks"."user_id" = "auth"."uid"())))));



ALTER TABLE "public"."tasks" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."ticket_messages" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "ticket_messages_access" ON "public"."ticket_messages" USING ((EXISTS ( SELECT 1
   FROM "public"."tickets"
  WHERE (("tickets"."id" = "ticket_messages"."ticket_id") AND (("tickets"."creator_id" = "auth"."uid"()) OR ("tickets"."assignee_id" = "auth"."uid"()) OR "public"."is_admin"())))));



CREATE POLICY "ticket_messages_insert" ON "public"."ticket_messages" FOR INSERT WITH CHECK ((("auth"."uid"() = "author_id") AND (EXISTS ( SELECT 1
   FROM "public"."tickets"
  WHERE (("tickets"."id" = "ticket_messages"."ticket_id") AND (("tickets"."creator_id" = "auth"."uid"()) OR ("tickets"."assignee_id" = "auth"."uid"())))))));



CREATE POLICY "ticket_messages_read" ON "public"."ticket_messages" FOR SELECT USING ((EXISTS ( SELECT 1
   FROM "public"."tickets"
  WHERE (("tickets"."id" = "ticket_messages"."ticket_id") AND (("tickets"."creator_id" = "auth"."uid"()) OR ("tickets"."assignee_id" = "auth"."uid"()))))));



ALTER TABLE "public"."ticket_status_history" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "ticket_status_history_access" ON "public"."ticket_status_history" FOR SELECT USING ((EXISTS ( SELECT 1
   FROM "public"."tickets"
  WHERE (("tickets"."id" = "ticket_status_history"."ticket_id") AND (("tickets"."creator_id" = "auth"."uid"()) OR ("tickets"."assignee_id" = "auth"."uid"()) OR "public"."is_admin"())))));



CREATE POLICY "ticket_status_history_insert" ON "public"."ticket_status_history" FOR INSERT WITH CHECK ((EXISTS ( SELECT 1
   FROM "public"."tickets"
  WHERE (("tickets"."id" = "ticket_status_history"."ticket_id") AND (("tickets"."creator_id" = "auth"."uid"()) OR ("tickets"."assignee_id" = "auth"."uid"()) OR "public"."is_admin"())))));



CREATE POLICY "ticket_status_history_read" ON "public"."ticket_status_history" FOR SELECT USING ((EXISTS ( SELECT 1
   FROM "public"."tickets"
  WHERE (("tickets"."id" = "ticket_status_history"."ticket_id") AND (("tickets"."creator_id" = "auth"."uid"()) OR ("tickets"."assignee_id" = "auth"."uid"()))))));



ALTER TABLE "public"."ticket_targets" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "ticket_targets_access" ON "public"."ticket_targets" USING ((EXISTS ( SELECT 1
   FROM "public"."tickets"
  WHERE (("tickets"."id" = "ticket_targets"."ticket_id") AND (("tickets"."creator_id" = "auth"."uid"()) OR ("tickets"."assignee_id" = "auth"."uid"()) OR "public"."is_admin"())))));



ALTER TABLE "public"."tickets" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "tickets_assignee_access" ON "public"."tickets" USING ((("auth"."uid"() = "assignee_id") OR "public"."is_admin"()));



CREATE POLICY "tickets_creator_access" ON "public"."tickets" USING ((("auth"."uid"() = "creator_id") OR "public"."is_admin"()));



ALTER TABLE "public"."user_notification_states" ENABLE ROW LEVEL SECURITY;


CREATE PUBLICATION "realtime_messages_publication_" WITH (publish = 'insert, update, delete, truncate');


ALTER PUBLICATION "realtime_messages_publication_" OWNER TO "postgres";




ALTER PUBLICATION "supabase_realtime" OWNER TO "postgres";


ALTER PUBLICATION "supabase_realtime" ADD TABLE ONLY "public"."tickets";



ALTER PUBLICATION "supabase_realtime" ADD TABLE ONLY "public"."user_notification_states";






GRANT USAGE ON SCHEMA "public" TO "postgres";
GRANT USAGE ON SCHEMA "public" TO "anon";
GRANT USAGE ON SCHEMA "public" TO "authenticated";
GRANT USAGE ON SCHEMA "public" TO "service_role";



GRANT ALL ON FUNCTION "public"."lquery_in"("cstring") TO "postgres";
GRANT ALL ON FUNCTION "public"."lquery_in"("cstring") TO "anon";
GRANT ALL ON FUNCTION "public"."lquery_in"("cstring") TO "authenticated";
GRANT ALL ON FUNCTION "public"."lquery_in"("cstring") TO "service_role";



GRANT ALL ON FUNCTION "public"."lquery_out"("public"."lquery") TO "postgres";
GRANT ALL ON FUNCTION "public"."lquery_out"("public"."lquery") TO "anon";
GRANT ALL ON FUNCTION "public"."lquery_out"("public"."lquery") TO "authenticated";
GRANT ALL ON FUNCTION "public"."lquery_out"("public"."lquery") TO "service_role";



GRANT ALL ON FUNCTION "public"."lquery_recv"("internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."lquery_recv"("internal") TO "anon";
GRANT ALL ON FUNCTION "public"."lquery_recv"("internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."lquery_recv"("internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."lquery_send"("public"."lquery") TO "postgres";
GRANT ALL ON FUNCTION "public"."lquery_send"("public"."lquery") TO "anon";
GRANT ALL ON FUNCTION "public"."lquery_send"("public"."lquery") TO "authenticated";
GRANT ALL ON FUNCTION "public"."lquery_send"("public"."lquery") TO "service_role";



GRANT ALL ON FUNCTION "public"."ltree_in"("cstring") TO "postgres";
GRANT ALL ON FUNCTION "public"."ltree_in"("cstring") TO "anon";
GRANT ALL ON FUNCTION "public"."ltree_in"("cstring") TO "authenticated";
GRANT ALL ON FUNCTION "public"."ltree_in"("cstring") TO "service_role";



GRANT ALL ON FUNCTION "public"."ltree_out"("public"."ltree") TO "postgres";
GRANT ALL ON FUNCTION "public"."ltree_out"("public"."ltree") TO "anon";
GRANT ALL ON FUNCTION "public"."ltree_out"("public"."ltree") TO "authenticated";
GRANT ALL ON FUNCTION "public"."ltree_out"("public"."ltree") TO "service_role";



GRANT ALL ON FUNCTION "public"."ltree_recv"("internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."ltree_recv"("internal") TO "anon";
GRANT ALL ON FUNCTION "public"."ltree_recv"("internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."ltree_recv"("internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."ltree_send"("public"."ltree") TO "postgres";
GRANT ALL ON FUNCTION "public"."ltree_send"("public"."ltree") TO "anon";
GRANT ALL ON FUNCTION "public"."ltree_send"("public"."ltree") TO "authenticated";
GRANT ALL ON FUNCTION "public"."ltree_send"("public"."ltree") TO "service_role";



GRANT ALL ON FUNCTION "public"."ltree_gist_in"("cstring") TO "postgres";
GRANT ALL ON FUNCTION "public"."ltree_gist_in"("cstring") TO "anon";
GRANT ALL ON FUNCTION "public"."ltree_gist_in"("cstring") TO "authenticated";
GRANT ALL ON FUNCTION "public"."ltree_gist_in"("cstring") TO "service_role";



GRANT ALL ON FUNCTION "public"."ltree_gist_out"("public"."ltree_gist") TO "postgres";
GRANT ALL ON FUNCTION "public"."ltree_gist_out"("public"."ltree_gist") TO "anon";
GRANT ALL ON FUNCTION "public"."ltree_gist_out"("public"."ltree_gist") TO "authenticated";
GRANT ALL ON FUNCTION "public"."ltree_gist_out"("public"."ltree_gist") TO "service_role";



GRANT ALL ON FUNCTION "public"."ltxtq_in"("cstring") TO "postgres";
GRANT ALL ON FUNCTION "public"."ltxtq_in"("cstring") TO "anon";
GRANT ALL ON FUNCTION "public"."ltxtq_in"("cstring") TO "authenticated";
GRANT ALL ON FUNCTION "public"."ltxtq_in"("cstring") TO "service_role";



GRANT ALL ON FUNCTION "public"."ltxtq_out"("public"."ltxtquery") TO "postgres";
GRANT ALL ON FUNCTION "public"."ltxtq_out"("public"."ltxtquery") TO "anon";
GRANT ALL ON FUNCTION "public"."ltxtq_out"("public"."ltxtquery") TO "authenticated";
GRANT ALL ON FUNCTION "public"."ltxtq_out"("public"."ltxtquery") TO "service_role";



GRANT ALL ON FUNCTION "public"."ltxtq_recv"("internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."ltxtq_recv"("internal") TO "anon";
GRANT ALL ON FUNCTION "public"."ltxtq_recv"("internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."ltxtq_recv"("internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."ltxtq_send"("public"."ltxtquery") TO "postgres";
GRANT ALL ON FUNCTION "public"."ltxtq_send"("public"."ltxtquery") TO "anon";
GRANT ALL ON FUNCTION "public"."ltxtq_send"("public"."ltxtquery") TO "authenticated";
GRANT ALL ON FUNCTION "public"."ltxtq_send"("public"."ltxtquery") TO "service_role";


























































































































































































GRANT ALL ON FUNCTION "public"."_lt_q_regex"("public"."ltree"[], "public"."lquery"[]) TO "postgres";
GRANT ALL ON FUNCTION "public"."_lt_q_regex"("public"."ltree"[], "public"."lquery"[]) TO "anon";
GRANT ALL ON FUNCTION "public"."_lt_q_regex"("public"."ltree"[], "public"."lquery"[]) TO "authenticated";
GRANT ALL ON FUNCTION "public"."_lt_q_regex"("public"."ltree"[], "public"."lquery"[]) TO "service_role";



GRANT ALL ON FUNCTION "public"."_lt_q_rregex"("public"."lquery"[], "public"."ltree"[]) TO "postgres";
GRANT ALL ON FUNCTION "public"."_lt_q_rregex"("public"."lquery"[], "public"."ltree"[]) TO "anon";
GRANT ALL ON FUNCTION "public"."_lt_q_rregex"("public"."lquery"[], "public"."ltree"[]) TO "authenticated";
GRANT ALL ON FUNCTION "public"."_lt_q_rregex"("public"."lquery"[], "public"."ltree"[]) TO "service_role";



GRANT ALL ON FUNCTION "public"."_ltq_extract_regex"("public"."ltree"[], "public"."lquery") TO "postgres";
GRANT ALL ON FUNCTION "public"."_ltq_extract_regex"("public"."ltree"[], "public"."lquery") TO "anon";
GRANT ALL ON FUNCTION "public"."_ltq_extract_regex"("public"."ltree"[], "public"."lquery") TO "authenticated";
GRANT ALL ON FUNCTION "public"."_ltq_extract_regex"("public"."ltree"[], "public"."lquery") TO "service_role";



GRANT ALL ON FUNCTION "public"."_ltq_regex"("public"."ltree"[], "public"."lquery") TO "postgres";
GRANT ALL ON FUNCTION "public"."_ltq_regex"("public"."ltree"[], "public"."lquery") TO "anon";
GRANT ALL ON FUNCTION "public"."_ltq_regex"("public"."ltree"[], "public"."lquery") TO "authenticated";
GRANT ALL ON FUNCTION "public"."_ltq_regex"("public"."ltree"[], "public"."lquery") TO "service_role";



GRANT ALL ON FUNCTION "public"."_ltq_rregex"("public"."lquery", "public"."ltree"[]) TO "postgres";
GRANT ALL ON FUNCTION "public"."_ltq_rregex"("public"."lquery", "public"."ltree"[]) TO "anon";
GRANT ALL ON FUNCTION "public"."_ltq_rregex"("public"."lquery", "public"."ltree"[]) TO "authenticated";
GRANT ALL ON FUNCTION "public"."_ltq_rregex"("public"."lquery", "public"."ltree"[]) TO "service_role";



GRANT ALL ON FUNCTION "public"."_ltree_compress"("internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."_ltree_compress"("internal") TO "anon";
GRANT ALL ON FUNCTION "public"."_ltree_compress"("internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."_ltree_compress"("internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."_ltree_consistent"("internal", "public"."ltree"[], smallint, "oid", "internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."_ltree_consistent"("internal", "public"."ltree"[], smallint, "oid", "internal") TO "anon";
GRANT ALL ON FUNCTION "public"."_ltree_consistent"("internal", "public"."ltree"[], smallint, "oid", "internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."_ltree_consistent"("internal", "public"."ltree"[], smallint, "oid", "internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."_ltree_extract_isparent"("public"."ltree"[], "public"."ltree") TO "postgres";
GRANT ALL ON FUNCTION "public"."_ltree_extract_isparent"("public"."ltree"[], "public"."ltree") TO "anon";
GRANT ALL ON FUNCTION "public"."_ltree_extract_isparent"("public"."ltree"[], "public"."ltree") TO "authenticated";
GRANT ALL ON FUNCTION "public"."_ltree_extract_isparent"("public"."ltree"[], "public"."ltree") TO "service_role";



GRANT ALL ON FUNCTION "public"."_ltree_extract_risparent"("public"."ltree"[], "public"."ltree") TO "postgres";
GRANT ALL ON FUNCTION "public"."_ltree_extract_risparent"("public"."ltree"[], "public"."ltree") TO "anon";
GRANT ALL ON FUNCTION "public"."_ltree_extract_risparent"("public"."ltree"[], "public"."ltree") TO "authenticated";
GRANT ALL ON FUNCTION "public"."_ltree_extract_risparent"("public"."ltree"[], "public"."ltree") TO "service_role";



GRANT ALL ON FUNCTION "public"."_ltree_gist_options"("internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."_ltree_gist_options"("internal") TO "anon";
GRANT ALL ON FUNCTION "public"."_ltree_gist_options"("internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."_ltree_gist_options"("internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."_ltree_isparent"("public"."ltree"[], "public"."ltree") TO "postgres";
GRANT ALL ON FUNCTION "public"."_ltree_isparent"("public"."ltree"[], "public"."ltree") TO "anon";
GRANT ALL ON FUNCTION "public"."_ltree_isparent"("public"."ltree"[], "public"."ltree") TO "authenticated";
GRANT ALL ON FUNCTION "public"."_ltree_isparent"("public"."ltree"[], "public"."ltree") TO "service_role";



GRANT ALL ON FUNCTION "public"."_ltree_penalty"("internal", "internal", "internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."_ltree_penalty"("internal", "internal", "internal") TO "anon";
GRANT ALL ON FUNCTION "public"."_ltree_penalty"("internal", "internal", "internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."_ltree_penalty"("internal", "internal", "internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."_ltree_picksplit"("internal", "internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."_ltree_picksplit"("internal", "internal") TO "anon";
GRANT ALL ON FUNCTION "public"."_ltree_picksplit"("internal", "internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."_ltree_picksplit"("internal", "internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."_ltree_r_isparent"("public"."ltree", "public"."ltree"[]) TO "postgres";
GRANT ALL ON FUNCTION "public"."_ltree_r_isparent"("public"."ltree", "public"."ltree"[]) TO "anon";
GRANT ALL ON FUNCTION "public"."_ltree_r_isparent"("public"."ltree", "public"."ltree"[]) TO "authenticated";
GRANT ALL ON FUNCTION "public"."_ltree_r_isparent"("public"."ltree", "public"."ltree"[]) TO "service_role";



GRANT ALL ON FUNCTION "public"."_ltree_r_risparent"("public"."ltree", "public"."ltree"[]) TO "postgres";
GRANT ALL ON FUNCTION "public"."_ltree_r_risparent"("public"."ltree", "public"."ltree"[]) TO "anon";
GRANT ALL ON FUNCTION "public"."_ltree_r_risparent"("public"."ltree", "public"."ltree"[]) TO "authenticated";
GRANT ALL ON FUNCTION "public"."_ltree_r_risparent"("public"."ltree", "public"."ltree"[]) TO "service_role";



GRANT ALL ON FUNCTION "public"."_ltree_risparent"("public"."ltree"[], "public"."ltree") TO "postgres";
GRANT ALL ON FUNCTION "public"."_ltree_risparent"("public"."ltree"[], "public"."ltree") TO "anon";
GRANT ALL ON FUNCTION "public"."_ltree_risparent"("public"."ltree"[], "public"."ltree") TO "authenticated";
GRANT ALL ON FUNCTION "public"."_ltree_risparent"("public"."ltree"[], "public"."ltree") TO "service_role";



GRANT ALL ON FUNCTION "public"."_ltree_same"("public"."ltree_gist", "public"."ltree_gist", "internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."_ltree_same"("public"."ltree_gist", "public"."ltree_gist", "internal") TO "anon";
GRANT ALL ON FUNCTION "public"."_ltree_same"("public"."ltree_gist", "public"."ltree_gist", "internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."_ltree_same"("public"."ltree_gist", "public"."ltree_gist", "internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."_ltree_union"("internal", "internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."_ltree_union"("internal", "internal") TO "anon";
GRANT ALL ON FUNCTION "public"."_ltree_union"("internal", "internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."_ltree_union"("internal", "internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."_ltxtq_exec"("public"."ltree"[], "public"."ltxtquery") TO "postgres";
GRANT ALL ON FUNCTION "public"."_ltxtq_exec"("public"."ltree"[], "public"."ltxtquery") TO "anon";
GRANT ALL ON FUNCTION "public"."_ltxtq_exec"("public"."ltree"[], "public"."ltxtquery") TO "authenticated";
GRANT ALL ON FUNCTION "public"."_ltxtq_exec"("public"."ltree"[], "public"."ltxtquery") TO "service_role";



GRANT ALL ON FUNCTION "public"."_ltxtq_extract_exec"("public"."ltree"[], "public"."ltxtquery") TO "postgres";
GRANT ALL ON FUNCTION "public"."_ltxtq_extract_exec"("public"."ltree"[], "public"."ltxtquery") TO "anon";
GRANT ALL ON FUNCTION "public"."_ltxtq_extract_exec"("public"."ltree"[], "public"."ltxtquery") TO "authenticated";
GRANT ALL ON FUNCTION "public"."_ltxtq_extract_exec"("public"."ltree"[], "public"."ltxtquery") TO "service_role";



GRANT ALL ON FUNCTION "public"."_ltxtq_rexec"("public"."ltxtquery", "public"."ltree"[]) TO "postgres";
GRANT ALL ON FUNCTION "public"."_ltxtq_rexec"("public"."ltxtquery", "public"."ltree"[]) TO "anon";
GRANT ALL ON FUNCTION "public"."_ltxtq_rexec"("public"."ltxtquery", "public"."ltree"[]) TO "authenticated";
GRANT ALL ON FUNCTION "public"."_ltxtq_rexec"("public"."ltxtquery", "public"."ltree"[]) TO "service_role";



GRANT ALL ON FUNCTION "public"."create_dataset_with_files"("p_user_id" "uuid", "p_name" "text", "p_description" "text", "p_category_id" integer, "p_variable_overrides" "jsonb", "p_file_paths" "text"[]) TO "anon";
GRANT ALL ON FUNCTION "public"."create_dataset_with_files"("p_user_id" "uuid", "p_name" "text", "p_description" "text", "p_category_id" integer, "p_variable_overrides" "jsonb", "p_file_paths" "text"[]) TO "authenticated";
GRANT ALL ON FUNCTION "public"."create_dataset_with_files"("p_user_id" "uuid", "p_name" "text", "p_description" "text", "p_category_id" integer, "p_variable_overrides" "jsonb", "p_file_paths" "text"[]) TO "service_role";



GRANT ALL ON FUNCTION "public"."create_dataset_with_files"("p_user_id" "uuid", "p_name" "text", "p_description" "text", "p_category_id" integer, "p_variable_overrides" "jsonb", "p_file_paths" "text"[], "p_file_types" "text"[]) TO "anon";
GRANT ALL ON FUNCTION "public"."create_dataset_with_files"("p_user_id" "uuid", "p_name" "text", "p_description" "text", "p_category_id" integer, "p_variable_overrides" "jsonb", "p_file_paths" "text"[], "p_file_types" "text"[]) TO "authenticated";
GRANT ALL ON FUNCTION "public"."create_dataset_with_files"("p_user_id" "uuid", "p_name" "text", "p_description" "text", "p_category_id" integer, "p_variable_overrides" "jsonb", "p_file_paths" "text"[], "p_file_types" "text"[]) TO "service_role";



GRANT ALL ON FUNCTION "public"."get_categories_with_key"("p_key" "text", "p_user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_categories_with_key"("p_key" "text", "p_user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_categories_with_key"("p_key" "text", "p_user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_datasets_with_key"("p_key" "text", "p_user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_datasets_with_key"("p_key" "text", "p_user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_datasets_with_key"("p_key" "text", "p_user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_next_available_task"("p_table_name" "text", "p_worker_id" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."get_next_available_task"("p_table_name" "text", "p_worker_id" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_next_available_task"("p_table_name" "text", "p_worker_id" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_or_create_notification_state"("p_user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_or_create_notification_state"("p_user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_or_create_notification_state"("p_user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_templates_with_key"("p_key" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."get_templates_with_key"("p_key" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_templates_with_key"("p_key" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_tree_skeleton"("with_counts" boolean) TO "anon";
GRANT ALL ON FUNCTION "public"."get_tree_skeleton"("with_counts" boolean) TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_tree_skeleton"("with_counts" boolean) TO "service_role";



GRANT ALL ON FUNCTION "public"."get_user_role"("user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_user_role"("user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_user_role"("user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_var_nodes_filtered"("p_key" "text", "p_user_id" "uuid", "p_dataset_id" integer, "p_template_id" integer) TO "anon";
GRANT ALL ON FUNCTION "public"."get_var_nodes_filtered"("p_key" "text", "p_user_id" "uuid", "p_dataset_id" integer, "p_template_id" integer) TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_var_nodes_filtered"("p_key" "text", "p_user_id" "uuid", "p_dataset_id" integer, "p_template_id" integer) TO "service_role";



GRANT ALL ON FUNCTION "public"."get_var_nodes_with_active_status"("p_key" "text", "p_user_id" "uuid", "p_dataset_id" integer) TO "anon";
GRANT ALL ON FUNCTION "public"."get_var_nodes_with_active_status"("p_key" "text", "p_user_id" "uuid", "p_dataset_id" integer) TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_var_nodes_with_active_status"("p_key" "text", "p_user_id" "uuid", "p_dataset_id" integer) TO "service_role";



GRANT ALL ON FUNCTION "public"."get_variable_sources_by_dataset"("p_key" "text", "p_user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_variable_sources_by_dataset"("p_key" "text", "p_user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_variable_sources_by_dataset"("p_key" "text", "p_user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."handle_storage_change"() TO "anon";
GRANT ALL ON FUNCTION "public"."handle_storage_change"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."handle_storage_change"() TO "service_role";



GRANT ALL ON FUNCTION "public"."index"("public"."ltree", "public"."ltree") TO "postgres";
GRANT ALL ON FUNCTION "public"."index"("public"."ltree", "public"."ltree") TO "anon";
GRANT ALL ON FUNCTION "public"."index"("public"."ltree", "public"."ltree") TO "authenticated";
GRANT ALL ON FUNCTION "public"."index"("public"."ltree", "public"."ltree") TO "service_role";



GRANT ALL ON FUNCTION "public"."index"("public"."ltree", "public"."ltree", integer) TO "postgres";
GRANT ALL ON FUNCTION "public"."index"("public"."ltree", "public"."ltree", integer) TO "anon";
GRANT ALL ON FUNCTION "public"."index"("public"."ltree", "public"."ltree", integer) TO "authenticated";
GRANT ALL ON FUNCTION "public"."index"("public"."ltree", "public"."ltree", integer) TO "service_role";



GRANT ALL ON FUNCTION "public"."is_admin"("user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."is_admin"("user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."is_admin"("user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."lca"("public"."ltree"[]) TO "postgres";
GRANT ALL ON FUNCTION "public"."lca"("public"."ltree"[]) TO "anon";
GRANT ALL ON FUNCTION "public"."lca"("public"."ltree"[]) TO "authenticated";
GRANT ALL ON FUNCTION "public"."lca"("public"."ltree"[]) TO "service_role";



GRANT ALL ON FUNCTION "public"."lca"("public"."ltree", "public"."ltree") TO "postgres";
GRANT ALL ON FUNCTION "public"."lca"("public"."ltree", "public"."ltree") TO "anon";
GRANT ALL ON FUNCTION "public"."lca"("public"."ltree", "public"."ltree") TO "authenticated";
GRANT ALL ON FUNCTION "public"."lca"("public"."ltree", "public"."ltree") TO "service_role";



GRANT ALL ON FUNCTION "public"."lca"("public"."ltree", "public"."ltree", "public"."ltree") TO "postgres";
GRANT ALL ON FUNCTION "public"."lca"("public"."ltree", "public"."ltree", "public"."ltree") TO "anon";
GRANT ALL ON FUNCTION "public"."lca"("public"."ltree", "public"."ltree", "public"."ltree") TO "authenticated";
GRANT ALL ON FUNCTION "public"."lca"("public"."ltree", "public"."ltree", "public"."ltree") TO "service_role";



GRANT ALL ON FUNCTION "public"."lca"("public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree") TO "postgres";
GRANT ALL ON FUNCTION "public"."lca"("public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree") TO "anon";
GRANT ALL ON FUNCTION "public"."lca"("public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree") TO "authenticated";
GRANT ALL ON FUNCTION "public"."lca"("public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree") TO "service_role";



GRANT ALL ON FUNCTION "public"."lca"("public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree") TO "postgres";
GRANT ALL ON FUNCTION "public"."lca"("public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree") TO "anon";
GRANT ALL ON FUNCTION "public"."lca"("public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree") TO "authenticated";
GRANT ALL ON FUNCTION "public"."lca"("public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree") TO "service_role";



GRANT ALL ON FUNCTION "public"."lca"("public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree") TO "postgres";
GRANT ALL ON FUNCTION "public"."lca"("public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree") TO "anon";
GRANT ALL ON FUNCTION "public"."lca"("public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree") TO "authenticated";
GRANT ALL ON FUNCTION "public"."lca"("public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree") TO "service_role";



GRANT ALL ON FUNCTION "public"."lca"("public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree") TO "postgres";
GRANT ALL ON FUNCTION "public"."lca"("public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree") TO "anon";
GRANT ALL ON FUNCTION "public"."lca"("public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree") TO "authenticated";
GRANT ALL ON FUNCTION "public"."lca"("public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree") TO "service_role";



GRANT ALL ON FUNCTION "public"."lca"("public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree") TO "postgres";
GRANT ALL ON FUNCTION "public"."lca"("public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree") TO "anon";
GRANT ALL ON FUNCTION "public"."lca"("public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree") TO "authenticated";
GRANT ALL ON FUNCTION "public"."lca"("public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree", "public"."ltree") TO "service_role";



GRANT ALL ON FUNCTION "public"."lt_q_regex"("public"."ltree", "public"."lquery"[]) TO "postgres";
GRANT ALL ON FUNCTION "public"."lt_q_regex"("public"."ltree", "public"."lquery"[]) TO "anon";
GRANT ALL ON FUNCTION "public"."lt_q_regex"("public"."ltree", "public"."lquery"[]) TO "authenticated";
GRANT ALL ON FUNCTION "public"."lt_q_regex"("public"."ltree", "public"."lquery"[]) TO "service_role";



GRANT ALL ON FUNCTION "public"."lt_q_rregex"("public"."lquery"[], "public"."ltree") TO "postgres";
GRANT ALL ON FUNCTION "public"."lt_q_rregex"("public"."lquery"[], "public"."ltree") TO "anon";
GRANT ALL ON FUNCTION "public"."lt_q_rregex"("public"."lquery"[], "public"."ltree") TO "authenticated";
GRANT ALL ON FUNCTION "public"."lt_q_rregex"("public"."lquery"[], "public"."ltree") TO "service_role";



GRANT ALL ON FUNCTION "public"."ltq_regex"("public"."ltree", "public"."lquery") TO "postgres";
GRANT ALL ON FUNCTION "public"."ltq_regex"("public"."ltree", "public"."lquery") TO "anon";
GRANT ALL ON FUNCTION "public"."ltq_regex"("public"."ltree", "public"."lquery") TO "authenticated";
GRANT ALL ON FUNCTION "public"."ltq_regex"("public"."ltree", "public"."lquery") TO "service_role";



GRANT ALL ON FUNCTION "public"."ltq_rregex"("public"."lquery", "public"."ltree") TO "postgres";
GRANT ALL ON FUNCTION "public"."ltq_rregex"("public"."lquery", "public"."ltree") TO "anon";
GRANT ALL ON FUNCTION "public"."ltq_rregex"("public"."lquery", "public"."ltree") TO "authenticated";
GRANT ALL ON FUNCTION "public"."ltq_rregex"("public"."lquery", "public"."ltree") TO "service_role";



GRANT ALL ON FUNCTION "public"."ltree2text"("public"."ltree") TO "postgres";
GRANT ALL ON FUNCTION "public"."ltree2text"("public"."ltree") TO "anon";
GRANT ALL ON FUNCTION "public"."ltree2text"("public"."ltree") TO "authenticated";
GRANT ALL ON FUNCTION "public"."ltree2text"("public"."ltree") TO "service_role";



GRANT ALL ON FUNCTION "public"."ltree_addltree"("public"."ltree", "public"."ltree") TO "postgres";
GRANT ALL ON FUNCTION "public"."ltree_addltree"("public"."ltree", "public"."ltree") TO "anon";
GRANT ALL ON FUNCTION "public"."ltree_addltree"("public"."ltree", "public"."ltree") TO "authenticated";
GRANT ALL ON FUNCTION "public"."ltree_addltree"("public"."ltree", "public"."ltree") TO "service_role";



GRANT ALL ON FUNCTION "public"."ltree_addtext"("public"."ltree", "text") TO "postgres";
GRANT ALL ON FUNCTION "public"."ltree_addtext"("public"."ltree", "text") TO "anon";
GRANT ALL ON FUNCTION "public"."ltree_addtext"("public"."ltree", "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."ltree_addtext"("public"."ltree", "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."ltree_cmp"("public"."ltree", "public"."ltree") TO "postgres";
GRANT ALL ON FUNCTION "public"."ltree_cmp"("public"."ltree", "public"."ltree") TO "anon";
GRANT ALL ON FUNCTION "public"."ltree_cmp"("public"."ltree", "public"."ltree") TO "authenticated";
GRANT ALL ON FUNCTION "public"."ltree_cmp"("public"."ltree", "public"."ltree") TO "service_role";



GRANT ALL ON FUNCTION "public"."ltree_compress"("internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."ltree_compress"("internal") TO "anon";
GRANT ALL ON FUNCTION "public"."ltree_compress"("internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."ltree_compress"("internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."ltree_consistent"("internal", "public"."ltree", smallint, "oid", "internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."ltree_consistent"("internal", "public"."ltree", smallint, "oid", "internal") TO "anon";
GRANT ALL ON FUNCTION "public"."ltree_consistent"("internal", "public"."ltree", smallint, "oid", "internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."ltree_consistent"("internal", "public"."ltree", smallint, "oid", "internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."ltree_decompress"("internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."ltree_decompress"("internal") TO "anon";
GRANT ALL ON FUNCTION "public"."ltree_decompress"("internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."ltree_decompress"("internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."ltree_eq"("public"."ltree", "public"."ltree") TO "postgres";
GRANT ALL ON FUNCTION "public"."ltree_eq"("public"."ltree", "public"."ltree") TO "anon";
GRANT ALL ON FUNCTION "public"."ltree_eq"("public"."ltree", "public"."ltree") TO "authenticated";
GRANT ALL ON FUNCTION "public"."ltree_eq"("public"."ltree", "public"."ltree") TO "service_role";



GRANT ALL ON FUNCTION "public"."ltree_ge"("public"."ltree", "public"."ltree") TO "postgres";
GRANT ALL ON FUNCTION "public"."ltree_ge"("public"."ltree", "public"."ltree") TO "anon";
GRANT ALL ON FUNCTION "public"."ltree_ge"("public"."ltree", "public"."ltree") TO "authenticated";
GRANT ALL ON FUNCTION "public"."ltree_ge"("public"."ltree", "public"."ltree") TO "service_role";



GRANT ALL ON FUNCTION "public"."ltree_gist_options"("internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."ltree_gist_options"("internal") TO "anon";
GRANT ALL ON FUNCTION "public"."ltree_gist_options"("internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."ltree_gist_options"("internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."ltree_gt"("public"."ltree", "public"."ltree") TO "postgres";
GRANT ALL ON FUNCTION "public"."ltree_gt"("public"."ltree", "public"."ltree") TO "anon";
GRANT ALL ON FUNCTION "public"."ltree_gt"("public"."ltree", "public"."ltree") TO "authenticated";
GRANT ALL ON FUNCTION "public"."ltree_gt"("public"."ltree", "public"."ltree") TO "service_role";



GRANT ALL ON FUNCTION "public"."ltree_isparent"("public"."ltree", "public"."ltree") TO "postgres";
GRANT ALL ON FUNCTION "public"."ltree_isparent"("public"."ltree", "public"."ltree") TO "anon";
GRANT ALL ON FUNCTION "public"."ltree_isparent"("public"."ltree", "public"."ltree") TO "authenticated";
GRANT ALL ON FUNCTION "public"."ltree_isparent"("public"."ltree", "public"."ltree") TO "service_role";



GRANT ALL ON FUNCTION "public"."ltree_le"("public"."ltree", "public"."ltree") TO "postgres";
GRANT ALL ON FUNCTION "public"."ltree_le"("public"."ltree", "public"."ltree") TO "anon";
GRANT ALL ON FUNCTION "public"."ltree_le"("public"."ltree", "public"."ltree") TO "authenticated";
GRANT ALL ON FUNCTION "public"."ltree_le"("public"."ltree", "public"."ltree") TO "service_role";



GRANT ALL ON FUNCTION "public"."ltree_lt"("public"."ltree", "public"."ltree") TO "postgres";
GRANT ALL ON FUNCTION "public"."ltree_lt"("public"."ltree", "public"."ltree") TO "anon";
GRANT ALL ON FUNCTION "public"."ltree_lt"("public"."ltree", "public"."ltree") TO "authenticated";
GRANT ALL ON FUNCTION "public"."ltree_lt"("public"."ltree", "public"."ltree") TO "service_role";



GRANT ALL ON FUNCTION "public"."ltree_ne"("public"."ltree", "public"."ltree") TO "postgres";
GRANT ALL ON FUNCTION "public"."ltree_ne"("public"."ltree", "public"."ltree") TO "anon";
GRANT ALL ON FUNCTION "public"."ltree_ne"("public"."ltree", "public"."ltree") TO "authenticated";
GRANT ALL ON FUNCTION "public"."ltree_ne"("public"."ltree", "public"."ltree") TO "service_role";



GRANT ALL ON FUNCTION "public"."ltree_penalty"("internal", "internal", "internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."ltree_penalty"("internal", "internal", "internal") TO "anon";
GRANT ALL ON FUNCTION "public"."ltree_penalty"("internal", "internal", "internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."ltree_penalty"("internal", "internal", "internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."ltree_picksplit"("internal", "internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."ltree_picksplit"("internal", "internal") TO "anon";
GRANT ALL ON FUNCTION "public"."ltree_picksplit"("internal", "internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."ltree_picksplit"("internal", "internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."ltree_risparent"("public"."ltree", "public"."ltree") TO "postgres";
GRANT ALL ON FUNCTION "public"."ltree_risparent"("public"."ltree", "public"."ltree") TO "anon";
GRANT ALL ON FUNCTION "public"."ltree_risparent"("public"."ltree", "public"."ltree") TO "authenticated";
GRANT ALL ON FUNCTION "public"."ltree_risparent"("public"."ltree", "public"."ltree") TO "service_role";



GRANT ALL ON FUNCTION "public"."ltree_same"("public"."ltree_gist", "public"."ltree_gist", "internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."ltree_same"("public"."ltree_gist", "public"."ltree_gist", "internal") TO "anon";
GRANT ALL ON FUNCTION "public"."ltree_same"("public"."ltree_gist", "public"."ltree_gist", "internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."ltree_same"("public"."ltree_gist", "public"."ltree_gist", "internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."ltree_textadd"("text", "public"."ltree") TO "postgres";
GRANT ALL ON FUNCTION "public"."ltree_textadd"("text", "public"."ltree") TO "anon";
GRANT ALL ON FUNCTION "public"."ltree_textadd"("text", "public"."ltree") TO "authenticated";
GRANT ALL ON FUNCTION "public"."ltree_textadd"("text", "public"."ltree") TO "service_role";



GRANT ALL ON FUNCTION "public"."ltree_union"("internal", "internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."ltree_union"("internal", "internal") TO "anon";
GRANT ALL ON FUNCTION "public"."ltree_union"("internal", "internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."ltree_union"("internal", "internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."ltreeparentsel"("internal", "oid", "internal", integer) TO "postgres";
GRANT ALL ON FUNCTION "public"."ltreeparentsel"("internal", "oid", "internal", integer) TO "anon";
GRANT ALL ON FUNCTION "public"."ltreeparentsel"("internal", "oid", "internal", integer) TO "authenticated";
GRANT ALL ON FUNCTION "public"."ltreeparentsel"("internal", "oid", "internal", integer) TO "service_role";



GRANT ALL ON FUNCTION "public"."ltxtq_exec"("public"."ltree", "public"."ltxtquery") TO "postgres";
GRANT ALL ON FUNCTION "public"."ltxtq_exec"("public"."ltree", "public"."ltxtquery") TO "anon";
GRANT ALL ON FUNCTION "public"."ltxtq_exec"("public"."ltree", "public"."ltxtquery") TO "authenticated";
GRANT ALL ON FUNCTION "public"."ltxtq_exec"("public"."ltree", "public"."ltxtquery") TO "service_role";



GRANT ALL ON FUNCTION "public"."ltxtq_rexec"("public"."ltxtquery", "public"."ltree") TO "postgres";
GRANT ALL ON FUNCTION "public"."ltxtq_rexec"("public"."ltxtquery", "public"."ltree") TO "anon";
GRANT ALL ON FUNCTION "public"."ltxtq_rexec"("public"."ltxtquery", "public"."ltree") TO "authenticated";
GRANT ALL ON FUNCTION "public"."ltxtq_rexec"("public"."ltxtquery", "public"."ltree") TO "service_role";



GRANT ALL ON FUNCTION "public"."merge_job_vars"("p_template_id" integer, "p_dataset_ids" integer[]) TO "anon";
GRANT ALL ON FUNCTION "public"."merge_job_vars"("p_template_id" integer, "p_dataset_ids" integer[]) TO "authenticated";
GRANT ALL ON FUNCTION "public"."merge_job_vars"("p_template_id" integer, "p_dataset_ids" integer[]) TO "service_role";



GRANT ALL ON FUNCTION "public"."nlevel"("public"."ltree") TO "postgres";
GRANT ALL ON FUNCTION "public"."nlevel"("public"."ltree") TO "anon";
GRANT ALL ON FUNCTION "public"."nlevel"("public"."ltree") TO "authenticated";
GRANT ALL ON FUNCTION "public"."nlevel"("public"."ltree") TO "service_role";



GRANT ALL ON FUNCTION "public"."remove_json_comments"("commented_json" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."remove_json_comments"("commented_json" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."remove_json_comments"("commented_json" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."remove_json_comments_optimized"("commented_json" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."remove_json_comments_optimized"("commented_json" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."remove_json_comments_optimized"("commented_json" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."set_task_status_with_check_and_lock"("taskid" integer, "oldstatus" "text", "newstatus" "text", "table_name" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."set_task_status_with_check_and_lock"("taskid" integer, "oldstatus" "text", "newstatus" "text", "table_name" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."set_task_status_with_check_and_lock"("taskid" integer, "oldstatus" "text", "newstatus" "text", "table_name" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."set_user_role"("user_id" "uuid", "user_role" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."set_user_role"("user_id" "uuid", "user_role" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."set_user_role"("user_id" "uuid", "user_role" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."subltree"("public"."ltree", integer, integer) TO "postgres";
GRANT ALL ON FUNCTION "public"."subltree"("public"."ltree", integer, integer) TO "anon";
GRANT ALL ON FUNCTION "public"."subltree"("public"."ltree", integer, integer) TO "authenticated";
GRANT ALL ON FUNCTION "public"."subltree"("public"."ltree", integer, integer) TO "service_role";



GRANT ALL ON FUNCTION "public"."subpath"("public"."ltree", integer) TO "postgres";
GRANT ALL ON FUNCTION "public"."subpath"("public"."ltree", integer) TO "anon";
GRANT ALL ON FUNCTION "public"."subpath"("public"."ltree", integer) TO "authenticated";
GRANT ALL ON FUNCTION "public"."subpath"("public"."ltree", integer) TO "service_role";



GRANT ALL ON FUNCTION "public"."subpath"("public"."ltree", integer, integer) TO "postgres";
GRANT ALL ON FUNCTION "public"."subpath"("public"."ltree", integer, integer) TO "anon";
GRANT ALL ON FUNCTION "public"."subpath"("public"."ltree", integer, integer) TO "authenticated";
GRANT ALL ON FUNCTION "public"."subpath"("public"."ltree", integer, integer) TO "service_role";



GRANT ALL ON FUNCTION "public"."sync_category_files"() TO "anon";
GRANT ALL ON FUNCTION "public"."sync_category_files"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."sync_category_files"() TO "service_role";



GRANT ALL ON FUNCTION "public"."sync_dataset_files"() TO "anon";
GRANT ALL ON FUNCTION "public"."sync_dataset_files"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."sync_dataset_files"() TO "service_role";



GRANT ALL ON FUNCTION "public"."sync_job_template_files"() TO "anon";
GRANT ALL ON FUNCTION "public"."sync_job_template_files"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."sync_job_template_files"() TO "service_role";



GRANT ALL ON FUNCTION "public"."text2ltree"("text") TO "postgres";
GRANT ALL ON FUNCTION "public"."text2ltree"("text") TO "anon";
GRANT ALL ON FUNCTION "public"."text2ltree"("text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."text2ltree"("text") TO "service_role";



GRANT ALL ON FUNCTION "public"."track_ticket_status_changes"() TO "anon";
GRANT ALL ON FUNCTION "public"."track_ticket_status_changes"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."track_ticket_status_changes"() TO "service_role";



GRANT ALL ON FUNCTION "public"."trg_set_path"() TO "anon";
GRANT ALL ON FUNCTION "public"."trg_set_path"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."trg_set_path"() TO "service_role";



GRANT ALL ON FUNCTION "public"."trg_sync_dataset_path"() TO "anon";
GRANT ALL ON FUNCTION "public"."trg_sync_dataset_path"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."trg_sync_dataset_path"() TO "service_role";



GRANT ALL ON FUNCTION "public"."trg_update_dataset_paths_on_category_change"() TO "anon";
GRANT ALL ON FUNCTION "public"."trg_update_dataset_paths_on_category_change"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."trg_update_dataset_paths_on_category_change"() TO "service_role";



GRANT ALL ON FUNCTION "public"."trg_update_descendant_paths"() TO "anon";
GRANT ALL ON FUNCTION "public"."trg_update_descendant_paths"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."trg_update_descendant_paths"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_category_levels_updated_at"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_category_levels_updated_at"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_category_levels_updated_at"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_dataset_with_files"("p_dataset_id" integer, "p_description" "text", "p_file_paths" "text"[], "p_file_types" "text"[], "p_name" "text", "p_user_id" "uuid", "p_variable_overrides" "jsonb") TO "anon";
GRANT ALL ON FUNCTION "public"."update_dataset_with_files"("p_dataset_id" integer, "p_description" "text", "p_file_paths" "text"[], "p_file_types" "text"[], "p_name" "text", "p_user_id" "uuid", "p_variable_overrides" "jsonb") TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_dataset_with_files"("p_dataset_id" integer, "p_description" "text", "p_file_paths" "text"[], "p_file_types" "text"[], "p_name" "text", "p_user_id" "uuid", "p_variable_overrides" "jsonb") TO "service_role";



GRANT ALL ON FUNCTION "public"."update_dataset_with_files"("p_dataset_id" integer, "p_user_id" "uuid", "p_name" "text", "p_description" "text", "p_category_id" integer, "p_variable_overrides" "jsonb", "p_file_ids" integer[]) TO "anon";
GRANT ALL ON FUNCTION "public"."update_dataset_with_files"("p_dataset_id" integer, "p_user_id" "uuid", "p_name" "text", "p_description" "text", "p_category_id" integer, "p_variable_overrides" "jsonb", "p_file_ids" integer[]) TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_dataset_with_files"("p_dataset_id" integer, "p_user_id" "uuid", "p_name" "text", "p_description" "text", "p_category_id" integer, "p_variable_overrides" "jsonb", "p_file_ids" integer[]) TO "service_role";



GRANT ALL ON FUNCTION "public"."update_dataset_with_files"("p_dataset_id" integer, "p_user_id" "uuid", "p_name" "text", "p_description" "text", "p_category_id" integer, "p_variable_overrides" "jsonb", "p_file_paths" "text"[]) TO "anon";
GRANT ALL ON FUNCTION "public"."update_dataset_with_files"("p_dataset_id" integer, "p_user_id" "uuid", "p_name" "text", "p_description" "text", "p_category_id" integer, "p_variable_overrides" "jsonb", "p_file_paths" "text"[]) TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_dataset_with_files"("p_dataset_id" integer, "p_user_id" "uuid", "p_name" "text", "p_description" "text", "p_category_id" integer, "p_variable_overrides" "jsonb", "p_file_paths" "text"[]) TO "service_role";



GRANT ALL ON FUNCTION "public"."update_dataset_with_files"("p_dataset_id" integer, "p_user_id" "uuid", "p_name" "text", "p_description" "text", "p_category_id" integer, "p_variable_overrides" "jsonb", "p_file_paths" "text"[], "p_file_types" "text"[]) TO "anon";
GRANT ALL ON FUNCTION "public"."update_dataset_with_files"("p_dataset_id" integer, "p_user_id" "uuid", "p_name" "text", "p_description" "text", "p_category_id" integer, "p_variable_overrides" "jsonb", "p_file_paths" "text"[], "p_file_types" "text"[]) TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_dataset_with_files"("p_dataset_id" integer, "p_user_id" "uuid", "p_name" "text", "p_description" "text", "p_category_id" integer, "p_variable_overrides" "jsonb", "p_file_paths" "text"[], "p_file_types" "text"[]) TO "service_role";



GRANT ALL ON FUNCTION "public"."update_descendant_paths"("category_id" integer, "old_path" "public"."ltree", "new_path" "public"."ltree") TO "anon";
GRANT ALL ON FUNCTION "public"."update_descendant_paths"("category_id" integer, "old_path" "public"."ltree", "new_path" "public"."ltree") TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_descendant_paths"("category_id" integer, "old_path" "public"."ltree", "new_path" "public"."ltree") TO "service_role";



GRANT ALL ON FUNCTION "public"."update_gui_components_updated_at"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_gui_components_updated_at"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_gui_components_updated_at"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_job_status_with_lock"("p_parent_job_id" integer, "p_child_task_status" "text", "p_parent_job_table_name" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."update_job_status_with_lock"("p_parent_job_id" integer, "p_child_task_status" "text", "p_parent_job_table_name" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_job_status_with_lock"("p_parent_job_id" integer, "p_child_task_status" "text", "p_parent_job_table_name" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."update_log_fields_updated_at"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_log_fields_updated_at"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_log_fields_updated_at"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_template_data"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_template_data"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_template_data"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_template_data_optimized"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_template_data_optimized"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_template_data_optimized"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_tickets_updated_at"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_tickets_updated_at"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_tickets_updated_at"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_updated_at_column"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_updated_at_column"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_updated_at_column"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_user_notification_states_updated_at"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_user_notification_states_updated_at"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_user_notification_states_updated_at"() TO "service_role";



GRANT ALL ON FUNCTION "public"."validate_file_types_array"("file_types" "jsonb") TO "anon";
GRANT ALL ON FUNCTION "public"."validate_file_types_array"("file_types" "jsonb") TO "authenticated";
GRANT ALL ON FUNCTION "public"."validate_file_types_array"("file_types" "jsonb") TO "service_role";



GRANT ALL ON FUNCTION "public"."verify_file_ownership"("p_file_id" integer, "p_object_type" "text", "p_object_id" integer) TO "anon";
GRANT ALL ON FUNCTION "public"."verify_file_ownership"("p_file_id" integer, "p_object_type" "text", "p_object_id" integer) TO "authenticated";
GRANT ALL ON FUNCTION "public"."verify_file_ownership"("p_file_id" integer, "p_object_type" "text", "p_object_id" integer) TO "service_role";


















GRANT ALL ON TABLE "public"."bulk_task_tasks" TO "anon";
GRANT ALL ON TABLE "public"."bulk_task_tasks" TO "authenticated";
GRANT ALL ON TABLE "public"."bulk_task_tasks" TO "service_role";



GRANT ALL ON TABLE "public"."categories" TO "anon";
GRANT ALL ON TABLE "public"."categories" TO "authenticated";
GRANT ALL ON TABLE "public"."categories" TO "service_role";



GRANT ALL ON SEQUENCE "public"."categories_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."categories_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."categories_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."category_files" TO "anon";
GRANT ALL ON TABLE "public"."category_files" TO "authenticated";
GRANT ALL ON TABLE "public"."category_files" TO "service_role";



GRANT ALL ON SEQUENCE "public"."category_files_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."category_files_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."category_files_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."category_job_templates" TO "anon";
GRANT ALL ON TABLE "public"."category_job_templates" TO "authenticated";
GRANT ALL ON TABLE "public"."category_job_templates" TO "service_role";



GRANT ALL ON TABLE "public"."category_levels" TO "anon";
GRANT ALL ON TABLE "public"."category_levels" TO "authenticated";
GRANT ALL ON TABLE "public"."category_levels" TO "service_role";



GRANT ALL ON SEQUENCE "public"."category_levels_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."category_levels_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."category_levels_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."dataset_files" TO "anon";
GRANT ALL ON TABLE "public"."dataset_files" TO "authenticated";
GRANT ALL ON TABLE "public"."dataset_files" TO "service_role";
GRANT SELECT ON TABLE "public"."dataset_files" TO PUBLIC;



GRANT ALL ON TABLE "public"."datasets" TO "anon";
GRANT ALL ON TABLE "public"."datasets" TO "authenticated";
GRANT ALL ON TABLE "public"."datasets" TO "service_role";



GRANT ALL ON SEQUENCE "public"."datasets_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."datasets_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."datasets_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."files" TO "anon";
GRANT ALL ON TABLE "public"."files" TO "authenticated";
GRANT ALL ON TABLE "public"."files" TO "service_role";



GRANT ALL ON SEQUENCE "public"."files_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."files_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."files_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."global_job_templates" TO "anon";
GRANT ALL ON TABLE "public"."global_job_templates" TO "authenticated";
GRANT ALL ON TABLE "public"."global_job_templates" TO "service_role";



GRANT ALL ON SEQUENCE "public"."global_job_templates_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."global_job_templates_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."global_job_templates_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."gui_components" TO "anon";
GRANT ALL ON TABLE "public"."gui_components" TO "authenticated";
GRANT ALL ON TABLE "public"."gui_components" TO "service_role";



GRANT ALL ON TABLE "public"."job_template_files" TO "anon";
GRANT ALL ON TABLE "public"."job_template_files" TO "authenticated";
GRANT ALL ON TABLE "public"."job_template_files" TO "service_role";
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."job_template_files" TO PUBLIC;



GRANT ALL ON TABLE "public"."jobs" TO "anon";
GRANT ALL ON TABLE "public"."jobs" TO "authenticated";
GRANT ALL ON TABLE "public"."jobs" TO "service_role";



GRANT ALL ON SEQUENCE "public"."jobs_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."jobs_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."jobs_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."log_fields" TO "anon";
GRANT ALL ON TABLE "public"."log_fields" TO "authenticated";
GRANT ALL ON TABLE "public"."log_fields" TO "service_role";



GRANT ALL ON SEQUENCE "public"."log_fields_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."log_fields_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."log_fields_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."profiles" TO "anon";
GRANT ALL ON TABLE "public"."profiles" TO "authenticated";
GRANT ALL ON TABLE "public"."profiles" TO "service_role";



GRANT ALL ON SEQUENCE "public"."profiles_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."profiles_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."profiles_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."task_results" TO "anon";
GRANT ALL ON TABLE "public"."task_results" TO "authenticated";
GRANT ALL ON TABLE "public"."task_results" TO "service_role";



GRANT ALL ON SEQUENCE "public"."task_results_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."task_results_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."task_results_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."tasks" TO "anon";
GRANT ALL ON TABLE "public"."tasks" TO "authenticated";
GRANT ALL ON TABLE "public"."tasks" TO "service_role";



GRANT ALL ON SEQUENCE "public"."tasks_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."tasks_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."tasks_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."ticket_messages" TO "anon";
GRANT ALL ON TABLE "public"."ticket_messages" TO "authenticated";
GRANT ALL ON TABLE "public"."ticket_messages" TO "service_role";



GRANT ALL ON SEQUENCE "public"."ticket_messages_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."ticket_messages_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."ticket_messages_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."ticket_status_history" TO "anon";
GRANT ALL ON TABLE "public"."ticket_status_history" TO "authenticated";
GRANT ALL ON TABLE "public"."ticket_status_history" TO "service_role";



GRANT ALL ON SEQUENCE "public"."ticket_status_history_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."ticket_status_history_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."ticket_status_history_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."ticket_targets" TO "anon";
GRANT ALL ON TABLE "public"."ticket_targets" TO "authenticated";
GRANT ALL ON TABLE "public"."ticket_targets" TO "service_role";



GRANT ALL ON SEQUENCE "public"."ticket_targets_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."ticket_targets_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."ticket_targets_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."tickets" TO "anon";
GRANT ALL ON TABLE "public"."tickets" TO "authenticated";
GRANT ALL ON TABLE "public"."tickets" TO "service_role";



GRANT ALL ON SEQUENCE "public"."tickets_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."tickets_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."tickets_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."user_notification_states" TO "anon";
GRANT ALL ON TABLE "public"."user_notification_states" TO "authenticated";
GRANT ALL ON TABLE "public"."user_notification_states" TO "service_role";



ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "service_role";






























RESET ALL;
