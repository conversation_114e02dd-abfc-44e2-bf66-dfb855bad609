/**
 * Variable state color definitions
 * Used by VariableStatusBadge and variable focus indicators
 */

export type VariableState = 'active' | 'overridden' | 'not-set' | 'defined-higher';

/**
 * Get the color for a variable state
 * These colors match the existing VariableStatusBadge implementation
 */
export const getVariableStateColor = (state: VariableState): string => {
  switch (state) {
    case 'active':
      return '#2e7d32'; // Darker Green for better visibility
    case 'overridden':
      return '#f57c00'; // Darker Orange for better contrast
    case 'defined-higher':
      return '#616161'; // Darker Gray for better visibility
    case 'not-set':
    default:
      return '#d32f2f'; // Darker Red for better contrast
  }
};

/**
 * Get the label for a variable state
 */
export const getVariableStateLabel = (state: VariableState): string => {
  switch (state) {
    case 'active':
      return 'Active';
    case 'overridden':
      return 'Overridden';
    case 'defined-higher':
      return 'Higher level';
    case 'not-set':
    default:
      return 'Not set';
  }
};