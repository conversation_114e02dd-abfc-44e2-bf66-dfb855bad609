// Test script to verify API key authentication works
const http = require('http');

function testApiEndpoint(path, method = 'GET', data = null) {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: 'localhost',
            port: 3000,
            path: path,
            method: method,
            headers: {
                'Content-Type': 'application/json',
                'x-api-key': 'test-api-key-123'
            }
        };

        const req = http.request(options, (res) => {
            let responseData = '';
            
            res.on('data', (chunk) => {
                responseData += chunk;
            });
            
            res.on('end', () => {
                try {
                    const parsed = JSON.parse(responseData);
                    resolve({
                        status: res.statusCode,
                        data: parsed
                    });
                } catch (e) {
                    resolve({
                        status: res.statusCode,
                        data: responseData
                    });
                }
            });
        });

        req.on('error', (err) => {
            reject(err);
        });

        if (data) {
            req.write(JSON.stringify(data));
        }
        
        req.end();
    });
}

async function runTests() {
    console.log('Testing API endpoints with API key authentication...\n');

    try {
        // Test 1: Tree API
        console.log('1. Testing /api/tree');
        const treeResult = await testApiEndpoint('/api/tree?withCounts=true');
        console.log(`Status: ${treeResult.status}`);
        if (treeResult.status === 200) {
            console.log('✅ Tree API works with API key');
            console.log(`Found ${treeResult.data.data?.length || 0} categories`);
        } else {
            console.log('❌ Tree API failed');
            console.log('Response:', treeResult.data);
        }

        // Test 2: Var-nodes API
        console.log('\n2. Testing /api/var-nodes');
        const varNodesResult = await testApiEndpoint('/api/var-nodes?key=ENVIRONMENT');
        console.log(`Status: ${varNodesResult.status}`);
        if (varNodesResult.status === 200) {
            console.log('✅ Var-nodes API works with API key');
            console.log(`Found ${varNodesResult.data.data?.length || 0} nodes with ENVIRONMENT key`);
        } else {
            console.log('❌ Var-nodes API failed');
            console.log('Response:', varNodesResult.data);
        }

        // Test 3: Job effective API
        console.log('\n3. Testing /api/job/effective');
        const jobEffectiveResult = await testApiEndpoint('/api/job/effective', 'POST', {
            templateId: 1,
            datasetIds: [15, 17]
        });
        console.log(`Status: ${jobEffectiveResult.status}`);
        if (jobEffectiveResult.status === 200) {
            console.log('✅ Job effective API works with API key');
            console.log(`Merged variables for ${jobEffectiveResult.data.data?.length || 0} datasets`);
        } else {
            console.log('❌ Job effective API failed');
            console.log('Response:', jobEffectiveResult.data);
        }

    } catch (error) {
        console.error('Test failed with error:', error.message);
    }
}

runTests();
