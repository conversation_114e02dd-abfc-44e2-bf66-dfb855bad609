import React, { useState, useRef } from 'react';
import {
  FormControl,
  FormLabel,
  Stack,
  Tooltip,
  Button,
  Box,
  Typography,
  Chip,
  IconButton,
  Alert
} from '@mui/material';
import {
  CloudUpload as UploadIcon,
  AttachFile as FileIcon,
  Delete as DeleteIcon,
  HelpOutline as HelpOutlineIcon
} from '@mui/icons-material';
import { useTemplateStore } from '../../lib/stores/templateStore';

interface FilePickerGUIProps {
  value: string | string[] | null;
  onChange: (value: string | string[] | null) => void;
  name: string;
  gui: {
    label?: string;
    tooltip?: string;
    multiple?: boolean;
    accept?: string; // File type restrictions (e.g., ".txt,.csv" or "image/*")
    maxSize?: number; // Max file size in bytes
    maxFiles?: number; // Max number of files (for multiple mode)
    [key: string]: any;
  };
  disabled?: boolean;
}

export default function FilePicker({
  value,
  onChange,
  name,
  gui,
  disabled = false
}: FilePickerGUIProps) {
  const { setValidationError, getValidationError } = useTemplateStore();
  const [touched, setTouched] = useState(false);
  const [dragOver, setDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const error = getValidationError(name);

  // Normalize value to array for easier handling
  const files = Array.isArray(value) ? value : (value ? [value] : []);
  const isMultiple = gui.multiple || false;
  const maxFiles = gui.maxFiles || (isMultiple ? 10 : 1);
  const maxSize = gui.maxSize || 10 * 1024 * 1024; // Default 10MB

  const validateFiles = (fileList: File[]): string | null => {
    if (fileList.length === 0) {
      return null; // Allow empty for optional fields
    }

    if (fileList.length > maxFiles) {
      return `Maximum ${maxFiles} file${maxFiles > 1 ? 's' : ''} allowed`;
    }

    for (const file of fileList) {
      if (file.size > maxSize) {
        return `File "${file.name}" exceeds maximum size of ${formatFileSize(maxSize)}`;
      }
    }

    return null;
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const handleFileSelect = (selectedFiles: FileList | null) => {
    if (!selectedFiles || selectedFiles.length === 0) return;

    const fileArray = Array.from(selectedFiles);
    const validationError = validateFiles(fileArray);
    
    setTouched(true);
    setValidationError(name, validationError);

    if (validationError) {
      return;
    }

    // Convert files to file paths/names for storage
    const filePaths = fileArray.map(file => file.name);
    
    if (isMultiple) {
      // For multiple files, append to existing or replace
      const newFiles = [...files, ...filePaths].slice(0, maxFiles);
      onChange(newFiles);
    } else {
      // For single file, replace
      onChange(filePaths[0] || null);
    }
  };

  const handleFileRemove = (index: number) => {
    const newFiles = files.filter((_, i) => i !== index);
    
    if (isMultiple) {
      onChange(newFiles.length > 0 ? newFiles : null);
    } else {
      onChange(null);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    if (!disabled) {
      setDragOver(true);
    }
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    
    if (disabled) return;
    
    const droppedFiles = e.dataTransfer.files;
    handleFileSelect(droppedFiles);
  };

  const handleButtonClick = () => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const helperText = touched ? error : '';

  return (
    <FormControl fullWidth error={!!error && touched}>
      {gui.label && (
        <Stack direction="row" spacing={1} alignItems="center" sx={{ mb: 1 }}>
          <FormLabel>
            {gui.label}
            {gui.tooltip && (
              <Tooltip title={<div dangerouslySetInnerHTML={{ __html: gui.tooltip }} />}>
                <HelpOutlineIcon
                  sx={{ ml: 1, fontSize: '1rem', verticalAlign: 'middle' }}
                />
              </Tooltip>
            )}
          </FormLabel>
        </Stack>
      )}

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        multiple={isMultiple}
        accept={gui.accept}
        onChange={(e) => handleFileSelect(e.target.files)}
        style={{ display: 'none' }}
        disabled={disabled}
      />

      {/* Drop zone */}
      <Box
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={handleButtonClick}
        sx={{
          border: 2,
          borderStyle: 'dashed',
          borderColor: dragOver ? 'primary.main' : (error && touched ? 'error.main' : 'grey.300'),
          borderRadius: 1,
          p: 2,
          textAlign: 'center',
          cursor: disabled ? 'not-allowed' : 'pointer',
          backgroundColor: dragOver ? 'action.hover' : (disabled ? 'action.disabledBackground' : 'background.paper'),
          transition: 'all 0.2s ease-in-out',
          '&:hover': disabled ? {} : {
            borderColor: 'primary.main',
            backgroundColor: 'action.hover'
          }
        }}
      >
        <Stack spacing={1} alignItems="center">
          <UploadIcon 
            sx={{ 
              fontSize: 40, 
              color: disabled ? 'action.disabled' : 'text.secondary' 
            }} 
          />
          <Typography variant="body2" color={disabled ? 'text.disabled' : 'text.secondary'}>
            {dragOver ? 'Drop files here' : 'Click to select files or drag and drop'}
          </Typography>
          {gui.accept && (
            <Typography variant="caption" color="text.secondary">
              Accepted: {gui.accept}
            </Typography>
          )}
          <Typography variant="caption" color="text.secondary">
            Max size: {formatFileSize(maxSize)}
            {isMultiple && ` • Max files: ${maxFiles}`}
          </Typography>
        </Stack>
      </Box>

      {/* Selected files display */}
      {files.length > 0 && (
        <Box sx={{ mt: 1 }}>
          <Typography variant="caption" color="text.secondary" sx={{ mb: 1, display: 'block' }}>
            Selected files:
          </Typography>
          <Stack spacing={1}>
            {files.map((file, index) => (
              <Chip
                key={index}
                icon={<FileIcon />}
                label={file}
                onDelete={disabled ? undefined : () => handleFileRemove(index)}
                deleteIcon={<DeleteIcon />}
                variant="outlined"
                size="small"
                sx={{ justifyContent: 'flex-start' }}
              />
            ))}
          </Stack>
        </Box>
      )}

      {/* Error display */}
      {helperText && (
        <Alert severity="error" sx={{ mt: 1 }}>
          {helperText}
        </Alert>
      )}
    </FormControl>
  );
}
