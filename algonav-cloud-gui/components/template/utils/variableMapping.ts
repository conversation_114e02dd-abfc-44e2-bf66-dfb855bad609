/**
 * @deprecated This file has been reorganized. Please use the new imports:
 * - Types: import from '@/types/variable'
 * - State utilities: import from '@/lib/utils/variableState'
 * - Change tracking: import from '@/lib/utils/changeTracking'
 * - Mapping functions: import from '@/lib/utils/variableMapping'
 *
 * This file will be removed in a future update.
 */

// Re-export for backward compatibility
export type { VariableWithContext, VariableChange, VariableSaveContext } from '@/types/variable';
export { determineVariableSaveContext } from '@/lib/utils/variableState';
export { VariableChangeTracker, groupChangesByContext } from '@/lib/utils/changeTracking';
export {
  variableWithContextToTemplateVariable,
  templateVariableToVariableWithContext
} from '@/lib/utils/variableMapping';
