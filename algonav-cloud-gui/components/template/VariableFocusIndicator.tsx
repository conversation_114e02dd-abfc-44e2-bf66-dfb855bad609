import React from 'react';
import { Box } from '@mui/material';
import { getVariableStateColor, type VariableState } from '../../lib/utils/variableColors';

interface VariableFocusIndicatorProps {
  isVisible: boolean;
  primaryState: VariableState;
  size?: number;
}

export const VariableFocusIndicator: React.FC<VariableFocusIndicatorProps> = ({
  isVisible,
  primaryState,
  size = 14
}) => {
  if (!isVisible) {
    return null;
  }

  const color = getVariableStateColor(primaryState);

  return (
    <Box
      sx={{
        width: size,
        height: size,
        borderRadius: '50%',
        backgroundColor: color,
        display: 'inline-block',
        marginLeft: 1,
        flexShrink: 0,
        border: `2px solid ${color}`,
        boxShadow: `0 0 8px ${color}80, 0 0 16px ${color}40`, // Stronger glow effect
        animation: 'focusPulse 1.5s infinite',
        '@keyframes focusPulse': {
          '0%': {
            opacity: 1,
            transform: 'scale(1)',
            boxShadow: `0 0 8px ${color}80, 0 0 16px ${color}40`
          },
          '50%': {
            opacity: 0.8,
            transform: 'scale(1.2)',
            boxShadow: `0 0 12px ${color}CC, 0 0 24px ${color}60`
          },
          '100%': {
            opacity: 1,
            transform: 'scale(1)',
            boxShadow: `0 0 8px ${color}80, 0 0 16px ${color}40`
          }
        }
      }}
      title={`Focused Variable (${primaryState})`}
    />
  );
};

export default VariableFocusIndicator;