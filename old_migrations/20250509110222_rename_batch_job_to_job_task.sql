-- Supabase migration script: rename Batch->Job, Job->Task and further refinements (bulk_job_tasks->bulk_task_tasks, job_results->task_results)

-- Step 1: Rename core tables (Batch->Job, Job->Task)
ALTER TABLE "public"."jobs" RENAME TO "tasks_temp_rename";
ALTER TABLE "public"."batches" RENAME TO "jobs";
ALTER TABLE "tasks_temp_rename" RENAME TO "tasks";

-- Step 2: Rename additional tables (job_results->task_results, bulk_job_tasks->bulk_task_tasks)
ALTER TABLE "public"."job_results" RENAME TO "task_results";
ALTER TABLE "public"."bulk_job_tasks" RENAME TO "bulk_task_tasks";

-- Step 3: Rename sequences
-- Sequence for original 'jobs.id' (now 'tasks.id')
ALTER SEQUENCE "public"."jobs_id_seq" RENAME TO "tasks_id_seq";
-- Sequence for original 'batches.id' (now 'jobs.id')
ALTER SEQUENCE "public"."batches_id_seq" RENAME TO "jobs_id_seq";
-- Sequence for original 'job_results.id' (now 'task_results.id')
ALTER SEQUENCE "public"."job_results_id_seq" RENAME TO "task_results_id_seq";

-- Step 4: Rename Primary Key constraints (which also renames their underlying indexes) and update sequence ownership

-- For 'tasks' table (formerly 'jobs')
ALTER TABLE "public"."tasks" RENAME CONSTRAINT "jobs_pkey" TO "tasks_pkey";
ALTER SEQUENCE "public"."tasks_id_seq" OWNED BY "public"."tasks"."id";

-- For 'jobs' table (formerly 'batches')
ALTER TABLE "public"."jobs" RENAME CONSTRAINT "batches_pkey" TO "jobs_pkey";
ALTER SEQUENCE "public"."jobs_id_seq" OWNED BY "public"."jobs"."id";

-- For 'task_results' table (formerly 'job_results')
ALTER TABLE "public"."task_results" RENAME CONSTRAINT "job_results_pkey" TO "task_results_pkey";
ALTER SEQUENCE "public"."task_results_id_seq" OWNED BY "public"."task_results"."id";

-- For 'bulk_task_tasks' table (formerly 'bulk_job_tasks')
ALTER TABLE "public"."bulk_task_tasks" RENAME CONSTRAINT "bulk_job_tasks_pkey" TO "bulk_task_tasks_pkey";

-- Step 5: Drop Foreign Key constraints that will be redefined
-- Constraints are dropped from the NEWLY RENAMED tables, using their ORIGINAL names.

-- FK from 'tasks' (formerly 'jobs') to 'jobs' (formerly 'batches')
ALTER TABLE "public"."tasks" DROP CONSTRAINT "jobs_batch_id_fkey";

-- FK from 'task_results' (formerly 'job_results') to 'tasks' (formerly 'jobs')
ALTER TABLE "public"."task_results" DROP CONSTRAINT "job_results_job_id_fkey";

-- FKs on 'bulk_task_tasks' (formerly 'bulk_job_tasks')
ALTER TABLE "public"."bulk_task_tasks" DROP CONSTRAINT "bulk_job_tasks_bulk_job_id_fkey";
ALTER TABLE "public"."bulk_task_tasks" DROP CONSTRAINT "bulk_job_tasks_task_id_fkey";

-- Step 6: Rename columns
-- In 'tasks' table (formerly 'jobs'): rename 'batch_id' to 'job_id'
ALTER TABLE "public"."tasks" RENAME COLUMN "batch_id" TO "job_id";

-- In 'task_results' table (formerly 'job_results'): rename 'job_id' to 'task_id'
ALTER TABLE "public"."task_results" RENAME COLUMN "job_id" TO "task_id";

-- In 'bulk_task_tasks' table (formerly 'bulk_job_tasks'): rename 'bulk_job_id' to 'bulk_task_id'
ALTER TABLE "public"."bulk_task_tasks" RENAME COLUMN "bulk_job_id" TO "bulk_task_id";

-- Step 7: Rename non-PK indexes on renamed columns/tables
-- Index on 'task_results.task_id' (formerly 'job_results.job_id')
ALTER INDEX "public"."idx_job_results_job_id" RENAME TO "idx_task_results_task_id";

-- Index on 'bulk_task_tasks.task_id' (formerly 'bulk_job_tasks.task_id')
ALTER INDEX "public"."idx_bulk_job_tasks_task_id" RENAME TO "idx_bulk_task_tasks_task_id";

-- Step 8: Add new/adjusted Foreign Key constraints
-- On 'tasks' table: 'job_id' (formerly 'batch_id') now references 'public.jobs.id'
ALTER TABLE "public"."tasks"
ADD CONSTRAINT "tasks_job_id_fkey"
FOREIGN KEY ("job_id") REFERENCES "public"."jobs"("id");

-- On 'task_results' table: 'task_id' (formerly 'job_id') now references 'public.tasks.id'
ALTER TABLE "public"."task_results"
ADD CONSTRAINT "task_results_task_id_fkey"
FOREIGN KEY ("task_id") REFERENCES "public"."tasks"("id") ON DELETE CASCADE;

-- On 'bulk_task_tasks' table:
-- 'bulk_task_id' (formerly 'bulk_job_id') now references 'public.tasks.id'
ALTER TABLE "public"."bulk_task_tasks"
ADD CONSTRAINT "bulk_task_tasks_bulk_task_id_fkey"
FOREIGN KEY ("bulk_task_id") REFERENCES "public"."tasks"("id") ON DELETE CASCADE;

-- 'task_id' (not renamed column) now references 'public.tasks.id'
ALTER TABLE "public"."bulk_task_tasks"
ADD CONSTRAINT "bulk_task_tasks_task_id_fkey"
FOREIGN KEY ("task_id") REFERENCES "public"."tasks"("id"); -- Original schema did not specify ON DELETE for this FK.

-- Step 9: Drop and Recreate RLS Policies for renamed tables/columns

-- Policy for 'bulk_task_tasks' (formerly 'bulk_job_tasks')
DROP POLICY IF EXISTS "bulk_job_tasks_policy" ON "public"."bulk_task_tasks";
CREATE POLICY "bulk_task_tasks_policy"
ON "public"."bulk_task_tasks"
AS permissive
FOR ALL
TO public
USING ((EXISTS ( SELECT 1
   FROM "public"."tasks"
  WHERE (("tasks".id = "bulk_task_tasks"."bulk_task_id") AND ("tasks".user_id = auth.uid())))));

-- Policy for 'task_results' (formerly 'job_results')
DROP POLICY IF EXISTS "job_results_policy" ON "public"."task_results";
CREATE POLICY "task_results_policy"
ON "public"."task_results"
AS permissive
FOR ALL
TO public
USING ((EXISTS ( SELECT 1
   FROM "public"."tasks"
  WHERE (("tasks".id = "task_results"."task_id") AND ("tasks".user_id = auth.uid())))));

-- Step 10: Modify functions affected by renames
-- Function `update_batch_status_with_lock` logic needs to reflect new table and column names.
-- Original intent: update a "batch's" status based on its "jobs'" statuses.
-- New intent: update a "job's" (formerly batch) status based on its "tasks'" (formerly job) statuses.
DROP FUNCTION IF EXISTS public.update_batch_status_with_lock(integer, text, text);
CREATE OR REPLACE FUNCTION public.update_job_status_with_lock(
    p_parent_job_id integer, -- ID of the 'job' (formerly 'batch') whose status is being updated
    p_child_task_status text,    -- Status of one of its 'tasks' (formerly 'job')
    p_parent_job_table_name text -- Name of the 'jobs' table (e.g., 'public.jobs')
)
 RETURNS text
 LANGUAGE plpgsql
AS $function$
DECLARE
    v_current_status TEXT;
    v_completed INT;
    v_failed INT;
    v_total INT;
    v_new_status TEXT;
BEGIN
    -- Lock the row in the parent 'jobs' table (formerly 'batches')
    EXECUTE format('SELECT status FROM public.%I WHERE id = $1 FOR UPDATE', p_parent_job_table_name)
    USING p_parent_job_id
    INTO v_current_status;

    -- Get total number of child 'tasks' (formerly 'jobs') for this parent 'job' (formerly 'batch')
    -- Querying new 'tasks' table on new 'job_id' column, which links back to the parent 'jobs' table ID
    EXECUTE format('SELECT COUNT(*) FROM public.tasks WHERE job_id = $1')
    USING p_parent_job_id
    INTO v_total;

    -- Parse current status of the parent 'job'
    IF v_current_status = 'queued' OR v_current_status IS NULL THEN -- Handle initial state
        v_completed := 0;
        v_failed := 0;
    ELSE
        -- Assuming status format "completed/failed" or "completed/failed/total"
        -- The total is now dynamically calculated, so we only need completed and failed from stored status.
        v_completed := COALESCE(split_part(v_current_status, '/', 1)::INT, 0);
        v_failed := COALESCE(split_part(v_current_status, '/', 2)::INT, 0);
    END IF;

    -- Update counters based on child 'task' status
    IF p_child_task_status = 'complete' THEN
        v_completed := v_completed + 1;
    ELSIF p_child_task_status = 'error' THEN
        v_failed := v_failed + 1;
    END IF;

    -- Construct new status string for the parent 'job'
    v_new_status := v_completed || '/' || v_failed || '/' || v_total;

    -- Update status in the parent 'jobs' table
    EXECUTE format('UPDATE public.%I SET status = $1 WHERE id = $2', p_parent_job_table_name)
    USING v_new_status, p_parent_job_id;

    RETURN v_new_status;
END;
$function$;

DROP FUNCTION IF EXISTS public.set_job_status_with_check_and_lock(integer, text, text, text);

CREATE OR REPLACE FUNCTION public.set_task_status_with_check_and_lock(taskid integer, oldstatus text, newstatus text, table_name text DEFAULT 'tasks'::text)
 RETURNS text
 LANGUAGE plpgsql
AS $function$
DECLARE
    affected_rows TEXT;
    query TEXT;
BEGIN
    -- Dynamically construct the SELECT query
    query := format('SELECT status FROM public.%I WHERE id = $1 FOR NO KEY UPDATE NOWAIT', table_name);

    -- Execute the dynamic query
    EXECUTE query INTO affected_rows USING taskid;

    IF affected_rows != oldstatus THEN
        RETURN 'Status mismatch';
    END IF;

    -- Dynamically construct the UPDATE query
    query := format('UPDATE public.%I SET status = $1 WHERE id = $2', table_name);

    -- Execute the dynamic UPDATE query
    EXECUTE query USING newstatus, taskid;

    RETURN 'Success';

EXCEPTION
    WHEN OTHERS THEN
        -- Return the exception message
        RETURN SQLERRM;
END;
$function$
;

-- End of migration script