-- Migration: Create Notification System
-- Creates user_notification_states table and enables Realtime for tickets

-- Step 1: Create user_notification_states table
CREATE TABLE "public"."user_notification_states" (
    "user_id" uuid NOT NULL,
    "last_seen_at" timestamp with time zone NOT NULL DEFAULT timezone('utc'::text, now()),
    "created_at" timestamp with time zone NOT NULL DEFAULT timezone('utc'::text, now()),
    "updated_at" timestamp with time zone NOT NULL DEFAULT timezone('utc'::text, now())
);

-- Step 2: Set primary key
ALTER TABLE "public"."user_notification_states" ADD CONSTRAINT "user_notification_states_pkey" 
    PRIMARY KEY (user_id);

-- Step 3: Enable Row Level Security
ALTER TABLE "public"."user_notification_states" ENABLE ROW LEVEL SECURITY;

-- Step 4: Add foreign key constraint to auth.users
ALTER TABLE "public"."user_notification_states" ADD CONSTRAINT "user_notification_states_user_id_fkey" 
    FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;

-- Step 5: Create indexes for performance
CREATE INDEX idx_user_notification_states_user_id ON public.user_notification_states USING btree (user_id);
CREATE INDEX idx_user_notification_states_last_seen_at ON public.user_notification_states USING btree (last_seen_at);

-- Step 6: Create trigger for automatic updated_at timestamp
CREATE OR REPLACE FUNCTION update_user_notification_states_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = timezone('utc'::text, now());
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_user_notification_states_updated_at
    BEFORE UPDATE ON public.user_notification_states
    FOR EACH ROW
    EXECUTE FUNCTION update_user_notification_states_updated_at();

-- Step 7: Create RLS policies
-- Users can only access their own notification state
CREATE POLICY "Users can view own notification state" ON "public"."user_notification_states"
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own notification state" ON "public"."user_notification_states"
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own notification state" ON "public"."user_notification_states"
    FOR UPDATE USING (auth.uid() = user_id);

-- Step 8: Enable Realtime for tickets table
ALTER PUBLICATION supabase_realtime ADD TABLE public.tickets;

-- Step 9: Enable Realtime for user_notification_states table (for future use)
ALTER PUBLICATION supabase_realtime ADD TABLE public.user_notification_states;

-- Step 10: Create helper function to get or create notification state
CREATE OR REPLACE FUNCTION get_or_create_notification_state(p_user_id uuid)
RETURNS TABLE(user_id uuid, last_seen_at timestamptz, created_at timestamptz, updated_at timestamptz)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Try to get existing state
    RETURN QUERY
    SELECT ns.user_id, ns.last_seen_at, ns.created_at, ns.updated_at
    FROM user_notification_states ns
    WHERE ns.user_id = p_user_id;
    
    -- If no state exists, create one
    IF NOT FOUND THEN
        INSERT INTO user_notification_states (user_id)
        VALUES (p_user_id)
        RETURNING user_notification_states.user_id, user_notification_states.last_seen_at, 
                  user_notification_states.created_at, user_notification_states.updated_at
        INTO user_id, last_seen_at, created_at, updated_at;
        
        RETURN NEXT;
    END IF;
END;
$$;

-- Step 11: Grant necessary permissions
GRANT SELECT, INSERT, UPDATE ON public.user_notification_states TO authenticated;
GRANT EXECUTE ON FUNCTION get_or_create_notification_state(uuid) TO authenticated;
