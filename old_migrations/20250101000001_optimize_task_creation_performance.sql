-- Migration to optimize task creation performance
-- Adds indexes to improve batch query performance for job creation

-- Add index on tasks.job_id for faster job-related queries
CREATE INDEX IF NOT EXISTS idx_tasks_job_id ON public.tasks(job_id);

-- Add index on tasks.user_id for faster user-specific queries
CREATE INDEX IF NOT EXISTS idx_tasks_user_id ON public.tasks(user_id);

-- Add composite index on tasks for common query patterns
CREATE INDEX IF NOT EXISTS idx_tasks_user_job_status ON public.tasks(user_id, job_id, status);

-- Add index on dataset_files.dataset_id for faster batch queries
CREATE INDEX IF NOT EXISTS idx_dataset_files_dataset_id ON public.dataset_files(dataset_id);

-- Add index on global_job_templates.user_id for user-specific template queries
CREATE INDEX IF NOT EXISTS idx_global_job_templates_user_id ON public.global_job_templates(user_id);

-- Add index on datasets.user_id for user-specific dataset queries
CREATE INDEX IF NOT EXISTS idx_datasets_user_id ON public.datasets(user_id);

-- Add index on jobs.user_id for user-specific job queries
CREATE INDEX IF NOT EXISTS idx_jobs_user_id ON public.jobs(user_id);

-- Add index on jobs.status for status-based queries
CREATE INDEX IF NOT EXISTS idx_jobs_status ON public.jobs(status);

-- Add composite index on jobs for common query patterns
CREATE INDEX IF NOT EXISTS idx_jobs_user_status_created ON public.jobs(user_id, status, created_at DESC);
