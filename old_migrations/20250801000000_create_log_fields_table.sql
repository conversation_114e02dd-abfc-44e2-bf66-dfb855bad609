-- Migration to create the log_fields table for storing log field configurations
-- This table will replace the mock data in logs_in_database.json

-- Create the log_fields table
CREATE TABLE IF NOT EXISTS "public"."log_fields" (
    "id" SERIAL PRIMARY KEY,
    "group_name" TEXT NOT NULL,
    "field_data" JSONB NOT NULL,
    "created_at" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add an index on group_name for faster lookups
CREATE INDEX IF NOT EXISTS "idx_log_fields_group_name" ON "public"."log_fields" ("group_name");

-- Create a function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_log_fields_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to automatically update the updated_at timestamp
CREATE TRIGGER trigger_log_fields_updated_at
BEFORE UPDATE ON "public"."log_fields"
FOR EACH ROW
EXECUTE FUNCTION update_log_fields_updated_at();

-- Enable row level security
ALTER TABLE "public"."log_fields" ENABLE ROW LEVEL SECURITY;

-- Create a policy to allow all authenticated users to read log_fields
CREATE POLICY "Allow all users to read log_fields" ON "public"."log_fields"
    FOR SELECT
    TO authenticated
    USING (true);

-- Create a policy to allow only admins to modify log_fields
-- Note: You may need to adjust this policy based on your authentication setup
CREATE POLICY "Allow admins to modify log_fields" ON "public"."log_fields"
    FOR ALL
    TO authenticated
    USING (auth.jwt() ? 'role' AND auth.jwt()->>'role' = 'admin');

-- Grant permissions to authenticated users
GRANT SELECT ON "public"."log_fields" TO authenticated;

-- Insert initial data from logs_in_database.json
-- We'll populate this in a separate script or function
