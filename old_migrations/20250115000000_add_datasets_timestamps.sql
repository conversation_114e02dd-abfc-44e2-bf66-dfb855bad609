-- Add created_at and updated_at timestamps to datasets table
-- Migration: 20250115000000_add_datasets_timestamps.sql

-- Add timestamp columns to datasets table
ALTER TABLE datasets 
ADD COLUMN created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
ADD COLUMN updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL;

-- <PERSON><PERSON> function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at on row updates
CREATE TRIGGER update_datasets_updated_at 
    BEFORE UPDATE ON datasets 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Update existing datasets with recent timestamps (last 30 days)
-- This will set created_at to random dates within the last 30 days
-- and updated_at to the same or later date
UPDATE datasets 
SET 
    created_at = NOW() - (RANDOM() * INTERVAL '30 days'),
    updated_at = created_at + (RANDOM() * INTERVAL '5 days')
WH<PERSON><PERSON> created_at IS NULL OR updated_at IS NULL;

-- Add comment to document the change
COMMENT ON COLUMN datasets.created_at IS 'Timestamp when the dataset was created';
COMMENT ON COLUMN datasets.updated_at IS 'Timestamp when the dataset was last updated';
