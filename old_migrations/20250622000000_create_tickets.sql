-- Migration: Create Ticketing System
-- Creates tickets, ticket_targets, ticket_messages, ticket_status_history tables
-- with proper ENUMs, triggers, and RLS policies

-- Step 1: Create ENUMs for ticket status and priority
CREATE TYPE "public"."ticket_status" AS ENUM (
    'open',
    'in_progress', 
    'waiting_on_customer',
    'resolved',
    'closed',
    'cancelled'
);

CREATE TYPE "public"."ticket_priority" AS ENUM (
    'low',
    'medium',
    'high',
    'urgent'
);

-- Step 2: Create sequences
CREATE SEQUENCE "public"."tickets_id_seq";
CREATE SEQUENCE "public"."ticket_targets_id_seq";
CREATE SEQUENCE "public"."ticket_messages_id_seq";
CREATE SEQUENCE "public"."ticket_status_history_id_seq";

-- Step 3: Create tickets table
CREATE TABLE "public"."tickets" (
    "id" integer NOT NULL DEFAULT nextval('tickets_id_seq'::regclass),
    "creator_id" uuid NOT NULL,
    "assignee_id" uuid,
    "title" text NOT NULL,
    "description" text,
    "status" ticket_status NOT NULL DEFAULT 'open',
    "priority" ticket_priority NOT NULL DEFAULT 'medium',
    "created_at" timestamp with time zone NOT NULL DEFAULT timezone('utc'::text, now()),
    "updated_at" timestamp with time zone NOT NULL DEFAULT timezone('utc'::text, now())
);

ALTER TABLE "public"."tickets" ENABLE ROW LEVEL SECURITY;

-- Step 4: Create ticket_targets table (polymorphic relationship)
CREATE TABLE "public"."ticket_targets" (
    "id" integer NOT NULL DEFAULT nextval('ticket_targets_id_seq'::regclass),
    "ticket_id" integer NOT NULL,
    "target_type" text NOT NULL CHECK (target_type IN ('job', 'task', 'dataset')),
    "target_id" integer NOT NULL
);

ALTER TABLE "public"."ticket_targets" ENABLE ROW LEVEL SECURITY;

-- Step 5: Create ticket_messages table
CREATE TABLE "public"."ticket_messages" (
    "id" integer NOT NULL DEFAULT nextval('ticket_messages_id_seq'::regclass),
    "ticket_id" integer NOT NULL,
    "author_id" uuid NOT NULL,
    "body" text NOT NULL,
    "created_at" timestamp with time zone NOT NULL DEFAULT timezone('utc'::text, now())
);

ALTER TABLE "public"."ticket_messages" ENABLE ROW LEVEL SECURITY;

-- Step 6: Create ticket_status_history table
CREATE TABLE "public"."ticket_status_history" (
    "id" integer NOT NULL DEFAULT nextval('ticket_status_history_id_seq'::regclass),
    "ticket_id" integer NOT NULL,
    "old_status" ticket_status,
    "new_status" ticket_status NOT NULL,
    "changed_by" uuid NOT NULL,
    "changed_at" timestamp with time zone NOT NULL DEFAULT timezone('utc'::text, now())
);

ALTER TABLE "public"."ticket_status_history" ENABLE ROW LEVEL SECURITY;

-- Step 7: Set sequence ownership
ALTER SEQUENCE "public"."tickets_id_seq" OWNED BY "public"."tickets"."id";
ALTER SEQUENCE "public"."ticket_targets_id_seq" OWNED BY "public"."ticket_targets"."id";
ALTER SEQUENCE "public"."ticket_messages_id_seq" OWNED BY "public"."ticket_messages"."id";
ALTER SEQUENCE "public"."ticket_status_history_id_seq" OWNED BY "public"."ticket_status_history"."id";

-- Step 8: Create indexes
CREATE UNIQUE INDEX tickets_pkey ON public.tickets USING btree (id);
CREATE UNIQUE INDEX ticket_targets_pkey ON public.ticket_targets USING btree (id);
CREATE UNIQUE INDEX ticket_messages_pkey ON public.ticket_messages USING btree (id);
CREATE UNIQUE INDEX ticket_status_history_pkey ON public.ticket_status_history USING btree (id);

-- Additional indexes for performance
CREATE INDEX idx_tickets_creator_id ON public.tickets USING btree (creator_id);
CREATE INDEX idx_tickets_assignee_id ON public.tickets USING btree (assignee_id);
CREATE INDEX idx_tickets_status ON public.tickets USING btree (status);
CREATE INDEX idx_tickets_created_at ON public.tickets USING btree (created_at);
CREATE INDEX idx_ticket_targets_ticket_id ON public.ticket_targets USING btree (ticket_id);
CREATE INDEX idx_ticket_targets_target ON public.ticket_targets USING btree (target_type, target_id);
CREATE INDEX idx_ticket_messages_ticket_id ON public.ticket_messages USING btree (ticket_id);
CREATE INDEX idx_ticket_status_history_ticket_id ON public.ticket_status_history USING btree (ticket_id);

-- Step 9: Add primary key constraints
ALTER TABLE "public"."tickets" ADD CONSTRAINT "tickets_pkey" PRIMARY KEY USING INDEX "tickets_pkey";
ALTER TABLE "public"."ticket_targets" ADD CONSTRAINT "ticket_targets_pkey" PRIMARY KEY USING INDEX "ticket_targets_pkey";
ALTER TABLE "public"."ticket_messages" ADD CONSTRAINT "ticket_messages_pkey" PRIMARY KEY USING INDEX "ticket_messages_pkey";
ALTER TABLE "public"."ticket_status_history" ADD CONSTRAINT "ticket_status_history_pkey" PRIMARY KEY USING INDEX "ticket_status_history_pkey";

-- Step 10: Add foreign key constraints
ALTER TABLE "public"."tickets" ADD CONSTRAINT "tickets_creator_id_fkey" 
    FOREIGN KEY (creator_id) REFERENCES auth.users(id) NOT VALID;
ALTER TABLE "public"."tickets" VALIDATE CONSTRAINT "tickets_creator_id_fkey";

ALTER TABLE "public"."tickets" ADD CONSTRAINT "tickets_assignee_id_fkey" 
    FOREIGN KEY (assignee_id) REFERENCES auth.users(id) NOT VALID;
ALTER TABLE "public"."tickets" VALIDATE CONSTRAINT "tickets_assignee_id_fkey";

ALTER TABLE "public"."ticket_targets" ADD CONSTRAINT "ticket_targets_ticket_id_fkey" 
    FOREIGN KEY (ticket_id) REFERENCES tickets(id) ON DELETE CASCADE NOT VALID;
ALTER TABLE "public"."ticket_targets" VALIDATE CONSTRAINT "ticket_targets_ticket_id_fkey";

ALTER TABLE "public"."ticket_messages" ADD CONSTRAINT "ticket_messages_ticket_id_fkey" 
    FOREIGN KEY (ticket_id) REFERENCES tickets(id) ON DELETE CASCADE NOT VALID;
ALTER TABLE "public"."ticket_messages" VALIDATE CONSTRAINT "ticket_messages_ticket_id_fkey";

ALTER TABLE "public"."ticket_messages" ADD CONSTRAINT "ticket_messages_author_id_fkey" 
    FOREIGN KEY (author_id) REFERENCES auth.users(id) NOT VALID;
ALTER TABLE "public"."ticket_messages" VALIDATE CONSTRAINT "ticket_messages_author_id_fkey";

ALTER TABLE "public"."ticket_status_history" ADD CONSTRAINT "ticket_status_history_ticket_id_fkey" 
    FOREIGN KEY (ticket_id) REFERENCES tickets(id) ON DELETE CASCADE NOT VALID;
ALTER TABLE "public"."ticket_status_history" VALIDATE CONSTRAINT "ticket_status_history_ticket_id_fkey";

ALTER TABLE "public"."ticket_status_history" ADD CONSTRAINT "ticket_status_history_changed_by_fkey"
    FOREIGN KEY (changed_by) REFERENCES auth.users(id) NOT VALID;
ALTER TABLE "public"."ticket_status_history" VALIDATE CONSTRAINT "ticket_status_history_changed_by_fkey";

-- Step 11: Add updated_by column to tickets table for tracking who made changes
ALTER TABLE "public"."tickets" ADD COLUMN "updated_by" uuid;
ALTER TABLE "public"."tickets" ADD CONSTRAINT "tickets_updated_by_fkey"
    FOREIGN KEY (updated_by) REFERENCES auth.users(id) NOT VALID;
ALTER TABLE "public"."tickets" VALIDATE CONSTRAINT "tickets_updated_by_fkey";

-- Step 12: Create trigger function for status history
CREATE OR REPLACE FUNCTION public.track_ticket_status_changes()
RETURNS TRIGGER AS $$
BEGIN
    -- Only insert if status actually changed
    IF OLD.status IS DISTINCT FROM NEW.status THEN
        INSERT INTO public.ticket_status_history (ticket_id, old_status, new_status, changed_by)
        VALUES (NEW.id, OLD.status, NEW.status, COALESCE(NEW.updated_by, NEW.creator_id));
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Step 13: Create trigger for automatic status history tracking
CREATE TRIGGER ticket_status_change_trigger
    AFTER UPDATE ON public.tickets
    FOR EACH ROW
    EXECUTE FUNCTION public.track_ticket_status_changes();

-- Step 14: Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_tickets_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = timezone('utc'::text, now());
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Step 15: Create trigger for automatic updated_at timestamp
CREATE TRIGGER update_tickets_updated_at_trigger
    BEFORE UPDATE ON public.tickets
    FOR EACH ROW
    EXECUTE FUNCTION public.update_tickets_updated_at();

-- Step 16: Create RLS policies

-- Tickets policies: creator and assignee can access, admins can access all
CREATE POLICY "tickets_creator_access" ON "public"."tickets"
    AS permissive FOR ALL TO public
    USING (auth.uid() = creator_id);

CREATE POLICY "tickets_assignee_access" ON "public"."tickets"
    AS permissive FOR ALL TO public
    USING (auth.uid() = assignee_id);

-- Ticket targets policies: access through ticket ownership
CREATE POLICY "ticket_targets_access" ON "public"."ticket_targets"
    AS permissive FOR ALL TO public
    USING (EXISTS (
        SELECT 1 FROM public.tickets
        WHERE tickets.id = ticket_targets.ticket_id
        AND (tickets.creator_id = auth.uid() OR tickets.assignee_id = auth.uid())
    ));

-- Ticket comments policies: ticket collaborators can read/write
CREATE POLICY "ticket_messages_read" ON "public"."ticket_messages"
    AS permissive FOR SELECT TO public
    USING (EXISTS (
        SELECT 1 FROM public.tickets
        WHERE tickets.id = ticket_messages.ticket_id
        AND (tickets.creator_id = auth.uid() OR tickets.assignee_id = auth.uid())
    ));

CREATE POLICY "ticket_messages_insert" ON "public"."ticket_messages"
    AS permissive FOR INSERT TO public
    WITH CHECK (
        auth.uid() = author_id AND
        EXISTS (
            SELECT 1 FROM public.tickets
            WHERE tickets.id = ticket_messages.ticket_id
            AND (tickets.creator_id = auth.uid() OR tickets.assignee_id = auth.uid())
        )
    );

-- Ticket status history policies: read-only for ticket collaborators
CREATE POLICY "ticket_status_history_read" ON "public"."ticket_status_history"
    AS permissive FOR SELECT TO public
    USING (EXISTS (
        SELECT 1 FROM public.tickets
        WHERE tickets.id = ticket_status_history.ticket_id
        AND (tickets.creator_id = auth.uid() OR tickets.assignee_id = auth.uid())
    ));

-- Step 17: Grant permissions to service_role
GRANT ALL ON TABLE "public"."tickets" TO "service_role";
GRANT ALL ON TABLE "public"."ticket_targets" TO "service_role";
GRANT ALL ON TABLE "public"."ticket_messages" TO "service_role";
GRANT ALL ON TABLE "public"."ticket_status_history" TO "service_role";

GRANT USAGE ON SEQUENCE "public"."tickets_id_seq" TO "service_role";
GRANT USAGE ON SEQUENCE "public"."ticket_targets_id_seq" TO "service_role";
GRANT USAGE ON SEQUENCE "public"."ticket_messages_id_seq" TO "service_role";
GRANT USAGE ON SEQUENCE "public"."ticket_status_history_id_seq" TO "service_role";
