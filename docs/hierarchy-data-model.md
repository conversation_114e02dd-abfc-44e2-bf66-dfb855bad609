# Flexible Hierarchy & Dataset Profile – Data-Model Specification  
last update: 2025-08-03

## 1 Goals
* Represent an **arbitrary, per-customer campaign hierarchy** (campaign → sub-campaign → …) where:
  * Each node can store variable/file overrides.
  * The set & names of hierarchy levels are configurable per customer.
* Provide **dataset profiles** describing which file types are mandatory/optional for a dataset.
* Preserve existing data (`categories`, `datasets`, `files`) with a **non-breaking migration**.

---

## 2 Tables & Types

### 2.1 `category_definitions`
| column | type | constraints | note |
|--------|------|-------------|------|
| id | serial PK | | |
| customer_id | uuid | FK → auth.users, NOT NULL | a “customer” is the billing root user / tenant |
| slug | text | UNIQUE (customer_id, slug) | e.g. `campaign`, `flight` |
| display_name | text | NOT NULL | i18n via JSONB if required |
| hierarchy_level | int | NOT NULL | 0 = top, 1, 2 … Used for depth validation |
| created_at | timestamptz | default now() | |

Purpose: defines the *labels* and *order* of hierarchy levels for one customer.

### 2.2 `categories` (renamed logically to **category_instances** in code)
| column | type | constraints | note |
|--------|------|-------------|------|
| id | serial PK | |
| user_id | uuid | FK → auth.users, NOT NULL |
| name | text | NOT NULL | campaign name, flight name, … |
| description | text | |
| parent_category_id | int | FK → categories.id, CHECK cycles | NULL for root |
| category_definition_id | int | FK → category_definitions.id, NOT NULL |
| variable_overrides | jsonb | default `{}` | can include `"__undefined__":true` marker |
| created_at / updated_at | timestamptz | defaults | |

Composite UNIQUE: (user_id, parent_category_id, name) to avoid duplicates on same branch.

### 2.3 `datasets`
Add column:
```
profile_id int FK → dataset_profiles.id NULL
```
Existing fields stay.

### 2.4 `dataset_profiles`
| column | type | constraints |
| id | serial PK |
| customer_id | uuid | FK → auth.users |
| name | text | NOT NULL |
| description | text |
| created_at | timestamptz |

### 2.5 `dataset_profile_file_rules`
| column | type | constraints |
| profile_id | int FK → dataset_profiles.id |
| file_type | text | NOT NULL (e.g. `RINEX`, `IMU`, `ANTX`) |
| required | bool | default true |
PK (profile_id, file_type).

---

## 3 Variable Inheritance Algorithm
```text
resolve(node_id, key):
  walk = []
  cur = node_id
  while cur is not null:
      row = select variable_overrides from categories where id=cur
      if key in row:
          val = row[key]
          if val is null OR (val ->> '__undefined__')::bool = true:
              return undefined
          return val
      walk.append(cur)
      cur = parent_category_id(cur)
  # finally look at dataset.variable_overrides if called from dataset context
  return default_from_template
```
The backend will expose  
`GET /api/effective-vars?nodeId=123` →  
```json
{
  "resolved": { "lever_arm": [0,0,0], "base_gravity": 9.80665 },
  "sources": {
      "lever_arm": { "level": "flight", "nodeId": 456 },
      "base_gravity": { "level": "campaign", "nodeId": 123 }
  }
}
```

---

## 4 RLS Policies (outline)
1. `category_definitions`: owner (customer_id) CRUD.
2. `categories`: allow select/update/delete where user_id = auth.uid().
3. Depth constraint: trigger to ensure `parent.hierarchy_level + 1 = definition.hierarchy_level`.
4. `dataset_profiles` & `dataset_profile_file_rules`: same owner policy.

---

## 5 API Contracts (REST)

| method | path | body | notes |
|--------|------|------|-------|
| GET | `/api/category-definitions` | – | admin only |
| POST | `/api/category-definitions` | {slug, displayName, level} | |
| GET | `/api/categories?parent=<id>` | – | lazy-load tree |
| POST | `/api/categories` | {name, parentId, definitionId, variableOverrides} | |
| GET | `/api/effective-vars?nodeId=<id>` | – | see algorithm |
| GET | `/api/dataset-profiles` | – | |
| POST | `/api/dataset-profiles` | {name, description, rules:[…]} | |

---

## 6 Migration Steps
1. **DDL**  
   a. create `category_definitions`, `dataset_profiles`, `dataset_profile_file_rules`.  
   b. alter `categories` add `category_definition_id` NOT NULL default 1.  
2. **Data backfill**  
   *For each user*: create default definitions `[campaign(level 0)]`, `[subcampaign(level 1)]`.  
   update all root categories → definition_id = campaign.id, children → subcampaign.id.
3. Drop default, add FK & NOT NULL constraints.
4. Re-create views if any, regenerate Supabase types.

---

## 7 Frontend Implications
* TreeView obtains nodes grouped by `hierarchy_level.display_name`.
* Variable Editor uses `/api/effective-vars` for live inheritance map.
* New wizard for dataset creation: select `profile` → render required/optional file pickers.

---

## 8 Open Questions
* UI for editing **category definitions** – admin-only?  
* How to handle extremely deep hierarchies (>4)? fold/unfold logic.

---

## 9 Timeline (indicative)
| week | deliverable |
|------|-------------|
| 0 | DB migration, backfill script |
| 1 | Backend APIs & resolver util |
| 2 | Frontend TreeView + Editor integration |
| 3 | Dataset profile UI & file-rule enforcement |
| 4 | QA, e2e tests, deploy |