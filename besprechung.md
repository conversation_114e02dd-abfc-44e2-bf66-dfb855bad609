Du:

#### **1. Einleitung und Ausgangspunkt**
Das Meeting begann mit der konkreten Fragestellung, wie die **Gravimetrie-Funktionalität** als vollwertiges Produkt in die bestehende GUI integriert werden kann. Diese Überlegung diente als Aufhänger für eine grundlegende Diskussion über die Systemarchitektur. Ein zentrales Beispiel war von Anfang an der **Hebelarm (Lever Arm)**, der typischerweise kampagnenweit gilt, aber datensatzspezifisch überschrieben werden können muss.

#### **2. Zentrale Herausforderung: Ein flexibles Hierarchie-System**
Die Kernherausforderung ist die Notwendigkeit, Einstellungen auf verschiedenen Ebenen verwalten zu können, um sowohl Einfachheit als auch Flexibilität zu gewährleisten.

*   **Hierarchie-Level:** Die vorgeschlagene Hierarchie soll flexibel sein und mehrere Ebenen wie **Global/Template**, **Kampagne**, **Unter-Kampagne** oder **Datensatz** umfassen. Ein Datensatz ist dabei immer nur einer Kampagne zugeordnet, um komplexe Multi-zu-Multi-Beziehungen zu vermeiden.
*   **Overrides:** Jede tiefere Ebene kann Werte von einer höheren Ebene erben oder sie explizit überschreiben (Override).
*   **Umgang mit undefinierten Werten:** Ein wichtiger Punkt war, dass Parameter explizit "nicht definiert" sein können, also keinen Default-Wert besitzen. Die GUI muss dies validieren und dem Nutzer klar anzeigen.

#### **3. Konkrete Anforderungen und neue, wiederverwendbare GUI-Komponenten**
Aus der Hierarchie-Diskussion wurden mehrere spezifische, wiederverwendbare GUI-Komponenten abgeleitet:

**a) Array-Komponente (Meta-Komponente)**
*   **Anforderung:** Für die Gravimetrie wird ein kampagnenweiter Parameter ("Base Gravity Value") benötigt, der eine **Liste von Flughafen-Parkpositionen** mit den zugehörigen Schwerewerten enthält.
*   **Lösungsidee:** Eine neue **"Array-Komponente"** (oder "Meta-Komponente"), die es dem Nutzer ermöglicht, dynamisch eine Liste von Einträgen zu erstellen und zu verwalten (z.B. über "Add Entry" und "Remove Entry"-Buttons).
*   **Wiederverwendbarkeit:** Diese Komponente ist universell einsetzbar, z.B. für die Definition von Zeitintervallen für **ZUPT** (Zero-Velocity Updates) oder **GNSS-Outages**.

**b) Zeitstempel- und Intervall-Komponente**
*   **Anforderung:** Eine essenzielle, flexible Komponente für die Eingabe von Zeitpunkten und -intervallen.
*   **Lösungsidee:** Bietet dem Nutzer verschiedene Eingabeformate an:
    *   Bürgerliches Datum und Uhrzeit (z.B. über eine Kalender- und Zeitauswahl).
    *   GPS-Woche und GPS-Sekunden (als Integer und Float).
    *   GPS-Woche, Wochentag und Sekunden des Tages.
*   Intern wird ein einheitliches Format (Nanosekunden seit 6. Januar 1980) verwendet. Die Komponente bietet dem Nutzer Live-Feedback durch die Umrechnung in ein lesbares Format.

**c) Koordinaten-Komponente**
*   **Anforderung:** Flexible Eingabe von geografischen Positionen.
*   **Lösungsidee:** Eine dedizierte "Positions-Komponente" mit wählbaren Formaten, um Umrechnungsfehler beim Nutzer zu vermeiden:
    *   Länge, Breite, Höhe in Dezimalgrad.
    *   Grad, Minuten und dezimale Sekunden (DMS).
    *   Grad und dezimale Minuten.
*   **Zukunftsidee:** Integration einer interaktiven Karte für die Positionsauswahl per Klick.

#### **4. UI/UX-Vorschlag zur Visualisierung der Hierarchie**
Um die komplexe Hierarchie für den Nutzer transparent und handhabbar zu machen, wurde ein konkreter UI/UX-Entwurf diskutiert:

*   **Side-by-Side-Ansicht:** Die GUI soll eine geteilte Ansicht erhalten.
    *   **Links:** Eine **Baumansicht (Tree-View)**, die die gesamte hierarchische Struktur (Kampagnen, Unterkampagnen, Datensätze) darstellt.
    *   **Rechts:** Der bekannte **Editor** für die Template-Einstellungen.
*   **Dynamische Visualisierung:** Wenn der Nutzer im rechten Editor eine Variable auswählt oder bearbeitet, reagiert die Baumansicht links **dynamisch** und visualisiert den Status dieser spezifischen Variable über die gesamte Hierarchie hinweg:
    *   Es wird angezeigt, auf welcher Ebene der Wert **explizit gesetzt** wurde (z.B. durch einen grünen Punkt oder Fettschrift).
    *   Es wird angezeigt, wo der Wert **geerbt** wird oder wo ein **Override** stattfindet.
    *   Durch Hovern über einen Knoten könnten die dort gesetzten Werte direkt angezeigt werden.

#### **5. Dateiverwaltung und Uploads**
Ein weiterer zentraler Punkt war die nahtlose Integration der Dateiverwaltung in das Hierarchie-System.

*   **Dateien als Variablen:** Es wurde beschlossen, **Dateien** (z.B. ANTX, RINEX, IMU-Rohdaten) logisch **wie normale Variablen zu behandeln**. Sie können ebenfalls auf jeder Ebene der Hierarchie (global, kampagnenweit, etc.) zugewiesen werden.
*   **Upload-Mechanismus:** Der bevorzugte Weg ist der **Bulk-Upload** über Protokolle wie WebDAV oder FTP in ein definiertes Dateisystem. Ein Cronjob könnte neue Dateien erkennen und für die Zuweisung in der GUI verfügbar machen.

#### **6. Applikationsspezifische Dataset-Konfigurationen (Pflicht- und optionale Dateien)**
Dieser neu hinzugefügte Punkt fasst die detaillierte Diskussion über die Erstellung von Datensätzen zusammen:

*   **Anwendungs-Profile:** Anstatt einer generischen Oberfläche wird das System mit **applikationsspezifischen Profilen** arbeiten (z.B. "Bahnvermessung", "Flugvermessung"). Diese Profile werden zunächst von den Entwicklern für den jeweiligen Kunden oder Anwendungsfall konfiguriert.
*   **Definition von Pflicht- und optionalen Dateien:** Jedes Profil legt fest, welche Dateitypen für einen vollständigen Datensatz benötigt werden.
    *   **Verpflichtende Dateien:** Müssen vom Nutzer zugewiesen werden (z.B. eine GNSS-Rover-Datei). In der GUI könnten diese mit einem Sternchen (*) markiert werden, um die Notwendigkeit zu verdeutlichen.
    *   **Optionale Dateien:** Können, müssen aber nicht zugewiesen werden. Beispiele hierfür sind GNSS Basisstation-Dateien (da auch eine PPP-Prozessierung ohne Basisstation möglich ist) oder IMU-Daten (für reine GNSS-Auswertungen).
*   **Geführter Nutzer-Workflow:** Wenn ein Nutzer auf "Neuen Datensatz erstellen" klickt, präsentiert die GUI eine Maske, die genau auf sein Anwendungsprofil zugeschnitten ist. Dies führt den Nutzer durch den Prozess, stellt die Datenqualität sicher und vereinfacht die Handhabung erheblich.

#### **7. Schlussfolgerungen und nächste Schritte**
*   **Priorität 1:** Die Implementierung der **flexiblen Hierarchie und der dazugehörigen Baum-Visualisierung** ist der wichtigste nächste Schritt.
*   **Priorität 2:** Entwicklung der **wiederverwendbaren GUI-Komponenten** (Zeit, Koordinate, Array).
*   **Zukunftsvision:** Langfristig könnte eine **KI-gestützte Automatisierung** die Zuweisung von Dateien vorschlagen, wobei Datenschutz (lokale Verarbeitung) eine zentrale Rolle spielt.
*   **Offene Punkte:** Die genaue Ausgestaltung der "Meta-Komponente", die Datenschutz-Aspekte der KI-Automatisierung und die Feinheiten der Validierung von undefinierten Parametern bleiben für zukünftige Diskussionen offen.

